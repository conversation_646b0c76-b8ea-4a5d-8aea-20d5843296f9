/*
 * Copyright (c) 2024, sakumisu
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifdef RT_USING_CHERRYUSB
#if 1
#include "usbd_core.h"
#include "usbd_audio.h"
#include "usbd_hid.h"
#include "ulog.h"

#define SOUND_DEVICE_NAME    "sound0"    /* Audio 设备名称 */
static rt_device_t snd_dev = RT_NULL;              /* Audio 设备句柄 */
/* 事件控制块 */
static struct rt_event audio_event;
static bool audio_mute = false;
#define EVENT_AUDIO_OPEN  (1 << 1)
#define EVENT_AUDIO_CLOSE (1 << 2)
#define EVENT_AUDIO_WRITE (1 << 3)

#define MAX_PACKETS_IN_ONE_TRANSFER 1

#define CHANNEL 2



#define USB_DESC_SIZ (unsigned long)(9 +                                       \
                                    AUDIO_AC_DESCRIPTOR_INIT_LEN(1) +         \
                                    AUDIO_SIZEOF_AC_INPUT_TERMINAL_DESC +     \
                                    AUDIO_SIZEOF_AC_FEATURE_UNIT_DESC(CHANNEL, 1) + \
                                    AUDIO_SIZEOF_AC_OUTPUT_TERMINAL_DESC +    \
                                    AUDIO_AS_DESCRIPTOR_INIT_LEN(1) +         \
                                    25)

#define USBD_VID           0xffff
#define USBD_PID           0xffff
#define USBD_MAX_POWER     100
#define USBD_LANGID_STRING 1033


#define EP_INTERVAL 0x01

#define AUDIO_OUT_EP 0x02

#define AUDIO_OUT_FU_ID 0x02
#define AUDIO_OUT_INTF_ID 0x01

/* AUDIO Class Config */
#define AUDIO_SPEAKER_FREQ            16000U
#define AUDIO_SPEAKER_FRAME_SIZE_BYTE 2u
#define AUDIO_SPEAKER_RESOLUTION_BIT  16u


#define AUDIO_SAMPLE_FREQ(frq) (uint8_t)(frq), (uint8_t)((frq >> 8)), (uint8_t)((frq >> 16))

/* AudioFreq * DataSize (2 bytes) * NumChannels (Stereo: 2) */
#define AUDIO_OUT_PACKET ((uint32_t)((AUDIO_SPEAKER_FREQ * AUDIO_SPEAKER_FRAME_SIZE_BYTE * CHANNEL + 999) / 1000))

#define AUDIO_AC_SIZ (AUDIO_SIZEOF_AC_HEADER_DESC(1) +          \
                      AUDIO_SIZEOF_AC_INPUT_TERMINAL_DESC +     \
                      AUDIO_SIZEOF_AC_FEATURE_UNIT_DESC(CHANNEL, 1) + \
                      AUDIO_SIZEOF_AC_OUTPUT_TERMINAL_DESC)

#define AUDIO_RX_BUFFER_SIZE (AUDIO_OUT_PACKET * 10)

#define HID_INT_EP          0x81
#define HID_INT_EP_SIZE     8
#define HID_INT_EP_INTERVAL 10

#define HID_KEYBOARD_REPORT_DESC_SIZE 45


const uint8_t audio_hid_descriptor[] = {
    USB_DEVICE_DESCRIPTOR_INIT(USB_1_1, 0xef, 0x02, 0x01, USBD_VID, USBD_PID, 0x0001, 0x01),
    USB_CONFIG_DESCRIPTOR_INIT(USB_DESC_SIZ, 0x03, 0x01, USB_CONFIG_SELF_POWERED, USBD_MAX_POWER),
    AUDIO_AC_DESCRIPTOR_INIT(0x00, 0x02, AUDIO_AC_SIZ, 0x00, 0x01),
    AUDIO_AC_INPUT_TERMINAL_DESCRIPTOR_INIT(0x01, AUDIO_TERMINAL_STREAMING, 0x02, 0x0003),
    AUDIO_AC_FEATURE_UNIT_DESCRIPTOR_INIT(AUDIO_OUT_FU_ID, 0x01, 0x01, 0x03, 0x00, 0x00),
    AUDIO_AC_OUTPUT_TERMINAL_DESCRIPTOR_INIT(0x03, AUDIO_OUTTERM_SPEAKER, AUDIO_OUT_FU_ID),
    AUDIO_AS_DESCRIPTOR_INIT(AUDIO_OUT_INTF_ID, 0x01, 0x02, AUDIO_SPEAKER_FRAME_SIZE_BYTE, AUDIO_SPEAKER_RESOLUTION_BIT, AUDIO_OUT_EP, 0x09, AUDIO_OUT_PACKET,
                             EP_INTERVAL, AUDIO_SAMPLE_FREQ_3B(AUDIO_SPEAKER_FREQ)),

    /************** Descriptor of Joystick Mouse interface ****************/
    /* 09 */
    0x09,                          /* bLength: Interface Descriptor size */
    USB_DESCRIPTOR_TYPE_INTERFACE, /* bDescriptorType: Interface descriptor type */
    0x02,                          /* bInterfaceNumber: Number of Interface */
    0x00,                          /* bAlternateSetting: Alternate setting */
    0x01,                          /* bNumEndpoints */
    0x03,                          /* bInterfaceClass: HID */
    0x01,                          /* bInterfaceSubClass : 1=BOOT, 0=no boot */
    0x01,                          /* nInterfaceProtocol : 0=none, 1=keyboard, 2=mouse */
    0,                             /* iInterface: Index of string descriptor */
    /******************** Descriptor of Joystick Mouse HID ********************/
    /* 18 */
    0x09,                    /* bLength: HID Descriptor size */
    HID_DESCRIPTOR_TYPE_HID, /* bDescriptorType: HID */
    0x11,                    /* bcdHID: HID Class Spec release number */
    0x01,
    0x00,                          /* bCountryCode: Hardware target country */
    0x01,                          /* bNumDescriptors: Number of HID class descriptors to follow */
    0x22,                          /* bDescriptorType */
    HID_KEYBOARD_REPORT_DESC_SIZE, /* wItemLength: Total length of Report descriptor */
    0x00,
    /******************** Descriptor of Mouse endpoint ********************/
    /* 27 */
    0x07,                         /* bLength: Endpoint Descriptor size */
    USB_DESCRIPTOR_TYPE_ENDPOINT, /* bDescriptorType: */
    HID_INT_EP,                   /* bEndpointAddress: Endpoint Address (IN) */
    0x03,                         /* bmAttributes: Interrupt endpoint */
    HID_INT_EP_SIZE,              /* wMaxPacketSize: 4 Byte max */
    0x00,
    HID_INT_EP_INTERVAL, /* bInterval: Polling Interval */
    /* 34 */
    ///////////////////////////////////////
    /// string0 descriptor
    ///////////////////////////////////////
    USB_LANGID_INIT(USBD_LANGID_STRING),
    ///////////////////////////////////////
    /// string1 descriptor
    ///////////////////////////////////////
    0x14,                       /* bLength */
    USB_DESCRIPTOR_TYPE_STRING, /* bDescriptorType */
    'L', 0x00,                  /* wcChar0 */
    'e', 0x00,                  /* wcChar1 */
    'e', 0x00,                  /* wcChar2 */
    'd', 0x00,                  /* wcChar3 */
    'a', 0x00,                  /* wcChar4 */
    'r', 0x00,                  /* wcChar5 */
    's', 0x00,                  /* wcChar6 */
    'o', 0x00,                  /* wcChar7 */
    'n', 0x00,                  /* wcChar8 */
    ///////////////////////////////////////
    /// string2 descriptor
    ///////////////////////////////////////
    0x1c,                       /* bLength */
    USB_DESCRIPTOR_TYPE_STRING, /* bDescriptorType */
    'L', 0x00,                  /* wcChar0 */
    'e', 0x00,                  /* wcChar1 */
    'e', 0x00,                  /* wcChar2 */
    'd', 0x00,                  /* wcChar3 */
    'a', 0x00,                  /* wcChar4 */
    'r', 0x00,                  /* wcChar5 */
    's', 0x00,                  /* wcChar6 */
    'o', 0x00,                  /* wcChar7 */
    'n', 0x00,                  /* wcChar8 */
    ' ', 0x00,                  /* wcChar9 */
    'U', 0x00,                  /* wcChar10 */
    'A', 0x00,                  /* wcChar11 */
    'C', 0x00,                  /* wcChar12 */

    ///////////////////////////////////////
    /// string3 descriptor
    ///////////////////////////////////////
    0x16,                       /* bLength */
    USB_DESCRIPTOR_TYPE_STRING, /* bDescriptorType */
    '2', 0x00,                  /* wcChar0 */
    '0', 0x00,                  /* wcChar1 */
    '2', 0x00,                  /* wcChar2 */
    '5', 0x00,                  /* wcChar3 */
    '0', 0x00,                  /* wcChar4 */
    '5', 0x00,                  /* wcChar5 */
    '0', 0x00,                  /* wcChar6 */
    '6', 0x00,                  /* wcChar7 */
    '0', 0x00,                  /* wcChar8 */
    '0', 0x00,                  /* wcChar9 */
    0x00
};

static const uint8_t hid_keyboard_report_desc[HID_KEYBOARD_REPORT_DESC_SIZE] = {
    0x05, 0x01, // USAGE_PAGE (Generic Desktop)
    0x09, 0x06, // USAGE (Keyboard)
    0xa1, 0x01, // COLLECTION (Application)
    0x05, 0x07, // USAGE_PAGE (Keyboard)
    0x19, 0xe0, // USAGE_MINIMUM (Keyboard LeftControl)
    0x29, 0xe7, // USAGE_MAXIMUM (Keyboard Right GUI)
    0x15, 0x00, // LOGICAL_MINIMUM (0)
    0x25, 0x01, // LOGICAL_MAXIMUM (1)
    0x75, 0x01, // REPORT_SIZE (1)
    0x95, 0x08, // REPORT_COUNT (8)
    0x81, 0x02, // INPUT (Data,Var,Abs)
    0x95, 0x01, // REPORT_COUNT (1)
    0x75, 0x08, // REPORT_SIZE (8)
    0x81, 0x03, // INPUT (Cnst,Var,Abs)
    0x95, 0x06, // REPORT_COUNT (6)
    0x75, 0x08, // REPORT_SIZE (8)
    0x15, 0x00, // LOGICAL_MINIMUM (0)
    0x25, 0xFF, // LOGICAL_MAXIMUM (255)
    0x05, 0x07, // USAGE_PAGE (Keyboard)
    0x19, 0x00, // USAGE_MINIMUM (Reserved (no event indicated))
    0x29, 0x65, // USAGE_MAXIMUM (Keyboard Application)
    0x81, 0x00, // INPUT (Data,Ary,Abs)
    0xc0        // END_COLLECTION
};

USB_NOCACHE_RAM_SECTION USB_MEM_ALIGNX uint8_t audio_read_buffer[AUDIO_RX_BUFFER_SIZE];
USB_NOCACHE_RAM_SECTION USB_MEM_ALIGNX uint8_t audio_read_buffer_2[AUDIO_RX_BUFFER_SIZE];
USB_NOCACHE_RAM_SECTION USB_MEM_ALIGNX uint8_t audio_read_buffer_0[AUDIO_RX_BUFFER_SIZE];


volatile bool audio_rx_flag = 0;
volatile uint16_t audio_rx_len = 0;

#define HID_STATE_IDLE 0
#define HID_STATE_BUSY 1

/*!< hid state ! Data can be sent only when state is idle  */
static volatile uint8_t hid_state = HID_STATE_IDLE;

static const uint8_t default_sampling_freq_table[] = {
    AUDIO_SAMPLE_FREQ_NUM(1),
    AUDIO_SAMPLE_FREQ_4B(16000),
    AUDIO_SAMPLE_FREQ_4B(16000),
    AUDIO_SAMPLE_FREQ_4B(0x00),
    // AUDIO_SAMPLE_FREQ_4B(32000),
    // AUDIO_SAMPLE_FREQ_4B(32000),
    // AUDIO_SAMPLE_FREQ_4B(0x00),
    // AUDIO_SAMPLE_FREQ_4B(44100),
    // AUDIO_SAMPLE_FREQ_4B(44100),
    // AUDIO_SAMPLE_FREQ_4B(0x00),
    // AUDIO_SAMPLE_FREQ_4B(48000),
    // AUDIO_SAMPLE_FREQ_4B(48000),
    // AUDIO_SAMPLE_FREQ_4B(0x00),
    // AUDIO_SAMPLE_FREQ_4B(96000),
    // AUDIO_SAMPLE_FREQ_4B(96000),
    // AUDIO_SAMPLE_FREQ_4B(0x00),
};
static void usbd_event_handler(uint8_t busid, uint8_t event)
{
    switch (event) {
        case USBD_EVENT_RESET:
            break;
        case USBD_EVENT_CONNECTED:
            break;
        case USBD_EVENT_DISCONNECTED:
            break;
        case USBD_EVENT_RESUME:
            break;
        case USBD_EVENT_SUSPEND:
            break;
        case USBD_EVENT_CONFIGURED:
            audio_rx_flag = 0;
            hid_state = HID_STATE_IDLE;
            break;
        case USBD_EVENT_SET_REMOTE_WAKEUP:
            break;
        case USBD_EVENT_CLR_REMOTE_WAKEUP:
            break;

        default:
            break;
    }
}

void usbd_audio_open(uint8_t busid, uint8_t intf)
{
    if (intf == AUDIO_OUT_INTF_ID) {
        /* setup first out ep read transfer */
        if(audio_rx_flag){
            rt_memset(audio_read_buffer, 0, AUDIO_RX_BUFFER_SIZE);
            usbd_ep_start_read(busid, AUDIO_OUT_EP, audio_read_buffer, AUDIO_RX_BUFFER_SIZE);
        } else {
            rt_memset(audio_read_buffer_2, 0, AUDIO_RX_BUFFER_SIZE);
            usbd_ep_start_read(busid, AUDIO_OUT_EP, audio_read_buffer_2, AUDIO_RX_BUFFER_SIZE);
        }
        rt_event_send(&audio_event, EVENT_AUDIO_OPEN);
        LOG_I("audio OPEN1\r\n");
    } 
}

void usbd_audio_close(uint8_t busid, uint8_t intf)
{
    extern int xlen;
    extern int xfer_len;
    extern int ep_count;
    extern int ep_count_2;
    if (intf == AUDIO_OUT_INTF_ID) {
        // audio_rx_flag = 0;
        rt_event_send(&audio_event, EVENT_AUDIO_WRITE);
        rt_event_send(&audio_event, EVENT_AUDIO_CLOSE);
        LOG_I("audio CLOSE1 %d %d %d %d\n",xlen, xfer_len, ep_count, ep_count_2);
    } 
}

void usbd_audio_out_callback(uint8_t busid, uint8_t ep, uint32_t nbytes)
{
    // USB_LOG_RAW("actual out len:%d\r\n", nbytes);
    audio_rx_len = nbytes;
    static int x = 0;
    x+=nbytes;
    // rt_kprintf("ret %d\n", x);
    audio_rx_flag = !audio_rx_flag;
    if(audio_rx_flag){
        rt_memset(audio_read_buffer, 0, AUDIO_RX_BUFFER_SIZE);
        usbd_ep_start_read(busid, AUDIO_OUT_EP, audio_read_buffer, AUDIO_RX_BUFFER_SIZE);
    } else {
        rt_memset(audio_read_buffer_2, 0, AUDIO_RX_BUFFER_SIZE);
        usbd_ep_start_read(busid, AUDIO_OUT_EP, audio_read_buffer_2, AUDIO_RX_BUFFER_SIZE);
    }
    rt_event_send(&audio_event, EVENT_AUDIO_WRITE);
}

static struct usbd_endpoint audio_out_ep = {
    .ep_cb = usbd_audio_out_callback,
    .ep_addr = AUDIO_OUT_EP
};

void usbd_hid_int_callback(uint8_t busid, uint8_t ep, uint32_t nbytes)
{
    hid_state = HID_STATE_IDLE;
    rt_kprintf("hid int callback\r\n");
}

static struct usbd_endpoint hid_in_ep = {
    .ep_cb = usbd_hid_int_callback,
    .ep_addr = HID_INT_EP
};
void usbd_audio_set_volume(uint8_t busid, uint8_t ep, uint8_t ch, int volume_db)
{
    (void)busid;
    (void)ep;
    (void)ch;
    (void)volume_db;
    LOG_I("set volume %d", volume_db);
}

int usbd_audio_get_volume(uint8_t busid, uint8_t ep, uint8_t ch)
{
    (void)busid;
    (void)ep;
    (void)ch;

    return 0;
}

void usbd_audio_set_mute(uint8_t busid, uint8_t ep, uint8_t ch, bool mute)
{
    (void)busid;
    (void)ep;
    (void)ch;
    audio_mute = mute;
    LOG_I("set mute %d", audio_mute);
}

bool usbd_audio_get_mute(uint8_t busid, uint8_t ep, uint8_t ch)
{
    (void)busid;
    (void)ep;
    (void)ch;

    return audio_mute;
}

void usbd_audio_set_sampling_freq(uint8_t busid, uint8_t ep, uint32_t sampling_freq)
{
    (void)busid;
    (void)ep;
    (void)sampling_freq;
    LOG_I("set sampling freq %d", sampling_freq);
}

uint32_t usbd_audio_get_sampling_freq(uint8_t busid, uint8_t ep)
{
    (void)busid;
    (void)ep;

    return 0;
}
void usbd_audio_get_sampling_freq_table(uint8_t busid, uint8_t ep, uint8_t **sampling_freq_table)
{
    if (ep == AUDIO_OUT_EP) {
        *sampling_freq_table = (uint8_t *)default_sampling_freq_table;
    }
}
struct usbd_interface intf0;
struct usbd_interface intf1;
struct usbd_interface intf2;


struct audio_entity_info audio_entity_table[] = {
    { .bEntityId = AUDIO_OUT_FU_ID,
      .bDescriptorSubtype = AUDIO_CONTROL_FEATURE_UNIT,
      .ep = AUDIO_OUT_EP },
};

void usbd_app_init(uint8_t busid, uintptr_t reg_base)
{
    usbd_desc_register(busid, audio_hid_descriptor);
    usbd_add_interface(busid, usbd_audio_init_intf(busid, &intf1, 0x0100, audio_entity_table, 1));
    usbd_add_interface(busid, usbd_audio_init_intf(busid, &intf2, 0x0100, audio_entity_table, 1));

    usbd_add_endpoint(busid, &audio_out_ep);

    usbd_add_interface(busid, usbd_hid_init_intf(busid, &intf0, hid_keyboard_report_desc, HID_KEYBOARD_REPORT_DESC_SIZE));
    usbd_add_endpoint(busid, &hid_in_ep);

    usbd_initialize(busid, reg_base, usbd_event_handler);
    void audio_thread_init(void);
    audio_thread_init();
}
void audio_thread_entry(void *param)
{
    int ret = RT_EOK;
    rt_uint32_t e;
    while (1) {
        ret = rt_event_recv(&audio_event, (EVENT_AUDIO_OPEN | EVENT_AUDIO_CLOSE | EVENT_AUDIO_WRITE) ,
                            RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR,
                            RT_WAITING_FOREVER, &e);
        if (ret != RT_EOK)
        {
            rt_kprintf("thread1: OR recv ret 0x%x event 0x%x\n", ret, e);
        }
        if (e & EVENT_AUDIO_OPEN) {
            /* 根据设备名称查找 Audio 设备，获取设备句柄 */
            if (snd_dev == RT_NULL) {
                snd_dev = rt_device_find(SOUND_DEVICE_NAME);
                if (snd_dev == RT_NULL) {
                    rt_kprintf("find %s failed\r\n", SOUND_DEVICE_NAME);
                }
            }
            if(snd_dev != RT_NULL)
            {
                /* 以只写方式打开 Audio 播放设备 */
                int ret = rt_device_open(snd_dev, RT_DEVICE_OFLAG_WRONLY);
                if(ret != RT_EOK){
                    rt_kprintf("open %s failed\r\n", SOUND_DEVICE_NAME);
                }
            }
        }
        static int x = 0;
        if (e & EVENT_AUDIO_WRITE) {
            if (audio_rx_len > 0) {
                x += audio_rx_len;
                if(audio_mute){
                    LOG_I("MUTE");
                    rt_memset(audio_read_buffer_0, 0, audio_rx_len);
                    // rt_device_write(snd_dev, 0, audio_read_buffer_0, audio_rx_len);
                } else if(audio_rx_flag){
                    rt_device_write(snd_dev, 0, audio_read_buffer_2, audio_rx_len);
                    // rt_kprintf("1 write %d\r\n", audio_rx_len);
                    // LOG_HEX("a", 8, audio_read_buffer_2, audio_rx_len);
                    // rt_kprintf("1 %d %x %x %x\n", x, audio_read_buffer_2[audio_rx_len - 3], audio_read_buffer_2[audio_rx_len - 2], audio_read_buffer_2[audio_rx_len - 1]);
                    // rt_kprintf("1 %x %x %x\n", audio_read_buffer_2[0], audio_read_buffer_2[1], audio_read_buffer_2[2]);
                } else {
                    rt_device_write(snd_dev, 0, audio_read_buffer, audio_rx_len);
                    // rt_kprintf("2 write %d\r\n", audio_rx_len);
                    // LOG_HEX("a", 8, audio_read_buffer, audio_rx_len);
                    // rt_kprintf("2 %d %x %x %x\n", x, audio_read_buffer[audio_rx_len - 3], audio_read_buffer[audio_rx_len - 2], audio_read_buffer[audio_rx_len - 1]);
                    // rt_kprintf("2 %x %x %x\n", audio_read_buffer[0], audio_read_buffer[1], audio_read_buffer[2]);
                }
                audio_rx_len = 0;
            }
           
        }
        if (e & EVENT_AUDIO_CLOSE) {
            if(snd_dev != RT_NULL)
            {
                int ret = rt_device_close(snd_dev);
                if(ret != RT_EOK){
                    rt_kprintf("close %s failed %d\r\n", SOUND_DEVICE_NAME, ret);
                }
            }
        }
    }
}
void audio_thread_init(void)
{
    /* 初始化事件对象 */
    int result;
    result = rt_event_init(&audio_event, "audio_event", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK)
    {
        rt_kprintf("init event failed.\n");
        return ;
    }
    
    rt_thread_t audio_thread = rt_thread_create("audio_thread", audio_thread_entry, RT_NULL, 1024, 5, 10);
    if (audio_thread != RT_NULL) {
        int ret = rt_thread_startup(audio_thread);
        if(ret != RT_EOK){
            rt_kprintf("audio thread startup failed %d\r\n", ret);
        }
    }
}
USB_NOCACHE_RAM_SECTION USB_MEM_ALIGNX uint8_t hid_write_buffer[64];

void hid_keyboard_test(bool pagedown)
{
    uint8_t busid = 0;
    uint8_t sendbuffer[8] = { 0x00, 0x00, HID_KBD_USAGE_PAGEUP, 0x00, 0x00, 0x00, 0x00, 0x00 };

    if(pagedown) {
        sendbuffer[2] = HID_KBD_USAGE_PAGEDOWN;
    } else {
        sendbuffer[2] = HID_KBD_USAGE_PAGEUP;
    }
    if(usb_device_is_configured(busid) == false) {
        return;
    }
    memset(hid_write_buffer, 0, sizeof(hid_write_buffer));
    memcpy(hid_write_buffer, sendbuffer, 8);
    hid_state = HID_STATE_BUSY;
    usbd_ep_start_write(busid, HID_INT_EP, hid_write_buffer, 16);
    rt_hw_us_delay(1000);
    // usbd_ep_start_write(busid, HID_INT_EP, hid_write_buffer, 8);
}

#else
#include "stdint.h"
#include "n32g45x.h"
#include "usb_type.h"
#include "usb_core.h"
#include "usb_lib.h"
#include "ulog.h"

#define SOUND_DEVICE_NAME    "sound0"    /* Audio 设备名称 */
static rt_device_t snd_dev = RT_NULL;              /* Audio 设备句柄 */
/* 事件控制块 */
static struct rt_event audio_event;

#define EVENT_AUDIO_OPEN  (1 << 1)
#define EVENT_AUDIO_CLOSE (1 << 2)
#define EVENT_AUDIO_WRITE (1 << 3)

#define USB_DEVICE_DESCRIPTOR_TYPE        0x01
#define USB_CONFIGURATION_DESCRIPTOR_TYPE 0x02
#define USB_STRING_DESCRIPTOR_TYPE        0x03
#define USB_INTERFACE_DESCRIPTOR_TYPE     0x04
#define USB_ENDPOINT_DESCRIPTOR_TYPE      0x05

#define HID_DESCRIPTOR_TYPE    0x21
#define CUSTOMHID_SIZ_HID_DESC 0x09
#define CUSTOMHID_OFF_HID_DESC 118

#define CUSTOMHID_SIZ_DEVICE_DESC               18
#define CUSTOMHID_SIZ_CONFIG_DESC               (157 - 16) // 109 // 
#define CUSTOMHID_SIZ_REPORT_DESC               66
#define CUSTOMHID_SIZ_STRING_LANGID             4
#define CUSTOMHID_SIZ_STRING_VENDOR             38
#define CUSTOMHID_SIZ_STRING_PRODUCT            (34 + 16)
#define CUSTOMHID_SIZ_STRING_SERIAL             26

#define STANDARD_ENDPOINT_DESC_SIZE 0x09

#define SPEAKER_SIZ_INTERFACE_DESC_SIZE               9

#define AUDIO_STANDARD_ENDPOINT_DESC_SIZE             0x09
#define AUDIO_STREAMING_ENDPOINT_DESC_SIZE            0x07
#define USB_DEVICE_CLASS_AUDIO                        0x01
#define AUDIO_SUBCLASS_AUDIOCONTROL                   0x01
#define AUDIO_SUBCLASS_AUDIOSTREAMING                 0x02
#define AUDIO_PROTOCOL_UNDEFINED                      0x00
#define AUDIO_STREAMING_GENERAL                       0x01
#define AUDIO_STREAMING_FORMAT_TYPE                   0x02

/* Audio Descriptor Types */
#define AUDIO_INTERFACE_DESCRIPTOR_TYPE               0x24
#define AUDIO_ENDPOINT_DESCRIPTOR_TYPE                0x25


/* Audio Control Interface Descriptor Subtypes */
#define AUDIO_CONTROL_HEADER                          0x01
#define AUDIO_CONTROL_INPUT_TERMINAL                  0x02
#define AUDIO_CONTROL_OUTPUT_TERMINAL                 0x03
#define AUDIO_CONTROL_FEATURE_UNIT                    0x06

#define AUDIO_INPUT_TERMINAL_DESC_SIZE                0x0C
#define AUDIO_OUTPUT_TERMINAL_DESC_SIZE               0x09
#define AUDIO_STREAMING_INTERFACE_DESC_SIZE           0x07

#define AUDIO_CONTROL_MUTE                            0x0001

#define AUDIO_FORMAT_TYPE_I                           0x01

#define USB_ENDPOINT_TYPE_ISOCHRONOUS                 0x01
#define AUDIO_ENDPOINT_GENERAL                        0x01

#define CustomHID_GetConfiguration USB_ProcessNop
//#define CustomHID_SetConfiguration          USB_ProcessNop
#define CustomHID_GetInterface       USB_ProcessNop
#define CustomHID_SetInterface       USB_ProcessNop
#define CustomHID_GetStatus          USB_ProcessNop
#define CustomHID_ClearFeature       USB_ProcessNop
#define CustomHID_SetEndPointFeature USB_ProcessNop
#define CustomHID_SetDeviceFeature   USB_ProcessNop
//#define CustomHID_SetDeviceAddress          USB_ProcessNop

#define REPORT_DESCRIPTOR 0x22
#define GET_CUR                           0x81
#define SET_CUR                           0x01

typedef enum _HID_REQUESTS
{
    GET_REPORT = 1,
    GET_IDLE,
    GET_PROTOCOL,

    SET_REPORT = 9,
    SET_IDLE,
    SET_PROTOCOL
} HID_REQUESTS;
typedef enum _RESUME_STATE
{
    RESUME_EXTERNAL,
    RESUME_INTERNAL,
    RESUME_LATER,
    RESUME_WAIT,
    RESUME_START,
    RESUME_ON,
    RESUME_OFF,
    RESUME_ESOF
} RESUME_STATE;

typedef enum _DEVICE_STATE
{
    UNCONNECTED,
    ATTACHED,
    POWERED,
    SUSPENDED,
    ADDRESSED,
    CONFIGURED
} DEVICE_STATE;


__IO uint32_t bDeviceState = UNCONNECTED; /* USB device status */
__IO bool fSuspendEnabled  = true;        /* true when suspend is possible */
__IO uint32_t EP[8];

struct
{
    __IO RESUME_STATE eState;
    __IO uint8_t bESOFcnt;
} ResumeS;

__IO uint32_t remotewakeupon = 0;


void CustomHID_init(void);
void CustomHID_Reset(void);
void CustomHID_SetConfiguration(void);
void CustomHID_SetDeviceAddress(void);
void CustomHID_Status_In(void);
void CustomHID_Status_Out(void);
USB_Result CustomHID_Data_Setup(uint8_t);
USB_Result CustomHID_NoData_Setup(uint8_t);
USB_Result CustomHID_Get_Interface_Setting(uint8_t Interface, uint8_t AlternateSetting);
uint8_t* CustomHID_GetDeviceDescriptor(uint16_t);
uint8_t* CustomHID_GetConfigDescriptor(uint16_t);
uint8_t* CustomHID_GetStringDescriptor(uint16_t);
USB_Result CustomHID_SetProtocol(void);
uint8_t* CustomHID_GetProtocolValue(uint16_t Length);
USB_Result CustomHID_SetProtocol(void);
uint8_t* CustomHID_GetReportDescriptor(uint16_t Length);
uint8_t* CustomHID_GetHIDDescriptor(uint16_t Length);
uint8_t *Mute_Command(uint16_t Length);
uint8_t* CustomHID_SetIdle(uint16_t Length);

void USB_Interrupts_Config(void)
{
    /* Select USBCLK source */
    RCC_ConfigUsbClk(RCC_USBCLK_SRC_PLLCLK_DIV3);
    /* Enable the USB clock */
    RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_USB, ENABLE);
    NVIC_InitType NVIC_InitStructure;
    EXTI_InitType EXTI_InitStructure;

    /* 2 bit for pre-emption priority, 2 bits for subpriority */
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);

    /* Enable the USB interrupt */
    NVIC_InitStructure.NVIC_IRQChannel                   = USB_LP_CAN1_RX0_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 2;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority        = 0;
    NVIC_InitStructure.NVIC_IRQChannelCmd                = ENABLE;
    NVIC_Init(&NVIC_InitStructure);
        
    // /* Enable and configure the priority of the USB_HP IRQ Channel*/
    // NVIC_InitStructure.NVIC_IRQChannel                   = USB_HP_CAN1_TX_IRQn;
    // NVIC_InitStructure.NVIC_IRQChannelSubPriority        = 1;
    // NVIC_Init(&NVIC_InitStructure);

    /* Enable the USB Wake-up interrupt */
    NVIC_InitStructure.NVIC_IRQChannel                   = USBWakeUp_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_Init(&NVIC_InitStructure);

    /* Configure the EXTI line 18 connected internally to the USB IP */
    EXTI_ClrITPendBit(EXTI_LINE18);
	EXTI_InitStruct(&EXTI_InitStructure);
    EXTI_InitStructure.EXTI_Line    = EXTI_LINE18;
    EXTI_InitStructure.EXTI_Mode    = EXTI_Mode_Interrupt;
    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising;
    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
    EXTI_InitPeripheral(&EXTI_InitStructure);
}
void usbd_app_init(uint8_t busid, uintptr_t reg_base)
{
    /* 根据设备名称查找 Audio 设备，获取设备句柄 */
    snd_dev = rt_device_find(SOUND_DEVICE_NAME);   
    USB_Interrupts_Config();
    USB_Init();
    void audio_thread_init(void);
    audio_thread_init();
}



/* USB Standard Device Descriptor */
const uint8_t CustomHID_DeviceDescriptor[CUSTOMHID_SIZ_DEVICE_DESC] =
{
        0x12,                       /*bLength */
        USB_DEVICE_DESCRIPTOR_TYPE, /*bDescriptorType*/
        0x00,                       /*bcdUSB */
        0x02,
        0x00,                       /*bDeviceClass*/
        0x00,                       /*bDeviceSubClass*/
        0x00,                       /*bDeviceProtocol*/
        0x40,                       /*bMaxPacketSize40*/
        0xF5,                       /*idVendor (0x19F5)*/
        0x19,
        0x30,                       /*idProduct = 0x5730*/
        0x57,
        0x00,                       /*bcdDevice rel. 2.00*/
        0x02,
        1,                          /*Index of string descriptor describing
                                                                                            manufacturer */
        2,                          /*Index of string descriptor describing
                                                                                         product*/
        3,                          /*Index of string descriptor describing the
                                                                                         device serial number */
        0x01                        /*bNumConfigurations*/
}; /* CustomHID_DeviceDescriptor */


/* USB Configuration Descriptor */
/*   All Descriptors (Configuration, Interface, Endpoint, Class, Vendor */
const uint8_t CustomHID_ConfigDescriptor[CUSTOMHID_SIZ_CONFIG_DESC] =
{
    0x09, /* bLength: Configuration Descriptor size */
    USB_CONFIGURATION_DESCRIPTOR_TYPE, /* bDescriptorType: Configuration */
    CUSTOMHID_SIZ_CONFIG_DESC,
    /* wTotalLength: Bytes returned */
    0x00,
    0x03,         /* bNumInterfaces: 3 interface */
    0x01,         /* bConfigurationValue: Configuration value */
    0x00,         /* iConfiguration: Index of string descriptor describing
                                                             the configuration*/
    0xC0,         /* bmAttributes: Self powered */
    0x32,         /* MaxPower 100 mA: this current is used for detecting Vbus */

    /* USB Speaker Standard interface descriptor */
    SPEAKER_SIZ_INTERFACE_DESC_SIZE,      /* bLength */
    USB_INTERFACE_DESCRIPTOR_TYPE,        /* bDescriptorType */
    0x00,                                 /* bInterfaceNumber */
    0x00,                                 /* bAlternateSetting */
    0x00,                                 /* bNumEndpoints */
    USB_DEVICE_CLASS_AUDIO,               /* bInterfaceClass */
    AUDIO_SUBCLASS_AUDIOCONTROL,          /* bInterfaceSubClass */
    AUDIO_PROTOCOL_UNDEFINED,             /* bInterfaceProtocol */
    0x00,                                 /* iInterface */
    /* 09 byte*/

    /* USB Speaker Class-specific AC Interface Descriptor */
    SPEAKER_SIZ_INTERFACE_DESC_SIZE,   /* bLength */
    AUDIO_INTERFACE_DESCRIPTOR_TYPE,      /* bDescriptorType */
    AUDIO_CONTROL_HEADER,                 /* bDescriptorSubtype */
    0x00,          /* 1.00 */             /* bcdADC */
    0x01,
    0x27,                                 /* wTotalLength = 39*/
    0x00,
    0x01,                                 /* bInCollection */
    0x01,                                 /* baInterfaceNr */
    /* 09 byte*/

    /* USB Speaker Input Terminal Descriptor */
    AUDIO_INPUT_TERMINAL_DESC_SIZE,       /* bLength */
    AUDIO_INTERFACE_DESCRIPTOR_TYPE,      /* bDescriptorType */
    AUDIO_CONTROL_INPUT_TERMINAL,         /* bDescriptorSubtype */
    0x01,                                 /* bTerminalID */
    0x01,                                 /* wTerminalType AUDIO_TERMINAL_USB_STREAMING   0x0101 */
    0x01,
    0x00,                                 /* bAssocTerminal */
    0x01,                                 /* bNrChannels */
    0x00,                                 /* wChannelConfig 0x0000  Mono */
    0x00,
    0x00,                                 /* iChannelNames */
    0x00,                                 /* iTerminal */
    /* 12 byte*/

    /* USB Speaker Audio Feature Unit Descriptor */
    0x09,                                 /* bLength */
    AUDIO_INTERFACE_DESCRIPTOR_TYPE,      /* bDescriptorType */
    AUDIO_CONTROL_FEATURE_UNIT,           /* bDescriptorSubtype */
    0x02,                                 /* bUnitID */
    0x01,                                 /* bSourceID */
    0x01,                                 /* bControlSize */
    AUDIO_CONTROL_MUTE,                   /* bmaControls(0) */
    0x00,                                 /* bmaControls(1) */
    0x00,                                 /* iTerminal */
    /* 09 byte*/

    /*USB Speaker Output Terminal Descriptor */
    0x09,      /* bLength */
    AUDIO_INTERFACE_DESCRIPTOR_TYPE,      /* bDescriptorType */
    AUDIO_CONTROL_OUTPUT_TERMINAL,        /* bDescriptorSubtype */
    0x03,                                 /* bTerminalID */
    0x01,                                 /* wTerminalType  0x0301*/
    0x03,
    0x00,                                 /* bAssocTerminal */
    0x02,                                 /* bSourceID */
    0x00,                                 /* iTerminal */
    /* 09 byte*/

    /* USB Speaker Standard AS Interface Descriptor - Audio Streaming Zero Bandwith */
    /* Interface 1, Alternate Setting 0                                             */
    SPEAKER_SIZ_INTERFACE_DESC_SIZE,  /* bLength */
    USB_INTERFACE_DESCRIPTOR_TYPE,        /* bDescriptorType */
    0x01,                                 /* bInterfaceNumber */
    0x00,                                 /* bAlternateSetting */
    0x00,                                 /* bNumEndpoints */
    USB_DEVICE_CLASS_AUDIO,               /* bInterfaceClass */
    AUDIO_SUBCLASS_AUDIOSTREAMING,        /* bInterfaceSubClass */
    AUDIO_PROTOCOL_UNDEFINED,             /* bInterfaceProtocol */
    0x00,                                 /* iInterface */
    /* 09 byte*/

    /* USB Speaker Standard AS Interface Descriptor - Audio Streaming Operational */
    /* Interface 1, Alternate Setting 1                                           */
    SPEAKER_SIZ_INTERFACE_DESC_SIZE,  /* bLength */
    USB_INTERFACE_DESCRIPTOR_TYPE,        /* bDescriptorType */
    0x01,                                 /* bInterfaceNumber */
    0x01,                                 /* bAlternateSetting */
    0x01,                                 /* bNumEndpoints */
    USB_DEVICE_CLASS_AUDIO,               /* bInterfaceClass */
    AUDIO_SUBCLASS_AUDIOSTREAMING,        /* bInterfaceSubClass */
    AUDIO_PROTOCOL_UNDEFINED,             /* bInterfaceProtocol */
    0x00,                                 /* iInterface */
    /* 09 byte*/

    /* USB Speaker Audio Streaming Interface Descriptor */
    AUDIO_STREAMING_INTERFACE_DESC_SIZE,  /* bLength */
    AUDIO_INTERFACE_DESCRIPTOR_TYPE,      /* bDescriptorType */
    AUDIO_STREAMING_GENERAL,              /* bDescriptorSubtype */
    0x01,                                 /* bTerminalLink */
    0x01,                                 /* bDelay */
    0x02,                                 /* wFormatTag AUDIO_FORMAT_PCM8  0x0002*/
    0x00,
    /* 07 byte*/

    /* USB Speaker Audio Type I Format Interface Descriptor */
    0x0B,                                 /* bLength */
    AUDIO_INTERFACE_DESCRIPTOR_TYPE,      /* bDescriptorType */
    AUDIO_STREAMING_FORMAT_TYPE,          /* bDescriptorSubtype */
    AUDIO_FORMAT_TYPE_I,                  /* bFormatType */
    0x01,                                 /* bNrChannels */
    0x01,                                 /* bSubFrameSize */
    16,                                    /* bBitResolution */
    0x01,                                 /* bSamFreqType */
    0x80,                                 /* tSamFreq 16000 = 0x3E80 */
    0x3E,
    0x00,
    /* 11 byte*/

    /* Endpoint 1 - Standard Descriptor */
    AUDIO_STANDARD_ENDPOINT_DESC_SIZE,    /* bLength */
    USB_ENDPOINT_DESCRIPTOR_TYPE,         /* bDescriptorType */
    0x02,                                 /* bEndpointAddress 1 out endpoint*/
    USB_ENDPOINT_TYPE_ISOCHRONOUS,        /* bmAttributes */
    0x40,                                 /* wMaxPacketSize 22 bytes*/
    0x00,
    0x01,                                 /* bInterval */
    0x00,                                 /* bRefresh */
    0x00,                                 /* bSynchAddress */
    /* 09 byte*/

    /* Endpoint - Audio Streaming Descriptor*/
    AUDIO_STREAMING_ENDPOINT_DESC_SIZE,   /* bLength */
    AUDIO_ENDPOINT_DESCRIPTOR_TYPE,       /* bDescriptorType */
    AUDIO_ENDPOINT_GENERAL,               /* bDescriptor */
    0x00,                                 /* bmAttributes */
    0x00,                                 /* bLockDelayUnits */
    0x00,                                 /* wLockDelay */
    0x00,
    /* 07 byte*/
        
    /************** Descriptor of Custom HID interface ****************/
    /* 09 */
    0x09,         /* bLength: Interface Descriptor size */
    USB_INTERFACE_DESCRIPTOR_TYPE,/* bDescriptorType: Interface descriptor type */
    0x02,         /* bInterfaceNumber: Number of Interface */
    0x00,         /* bAlternateSetting: Alternate setting */
    0x02,         /* bNumEndpoints */
    0x03,         /* bInterfaceClass: HID */
    0x01,         /* bInterfaceSubClass : 1=BOOT, 0=no boot */
    0x01,         /* nInterfaceProtocol : 0=none, 1=keyboard, 2=mouse */
    0,            /* iInterface: Index of string descriptor */
    /******************** Descriptor of Custom HID HID ********************/
    /* 18 */
    0x09,         /* bLength: HID Descriptor size */
    HID_DESCRIPTOR_TYPE, /* bDescriptorType: HID */
    0x10,         /* bcdHID: HID Class Spec release number */
    0x01,
    0x00,         /* bCountryCode: Hardware target country */
    0x01,         /* bNumDescriptors: Number of HID class descriptors to follow */
    0x22,         /* bDescriptorType */
    CUSTOMHID_SIZ_REPORT_DESC,/* wItemLength: Total length of Report descriptor */
    0x00,
    /******************** Descriptor of Custom HID endpoints ******************/
    /* 27 */
    0x07,          /* bLength: Endpoint Descriptor size */
    USB_ENDPOINT_DESCRIPTOR_TYPE, /* bDescriptorType: */

    0x81,          /* bEndpointAddress: Endpoint Address (IN) */
    0x03,          /* bmAttributes: Interrupt endpoint */
    0x08,          /* wMaxPacketSize: 8 Bytes max */
    0x00,
    0x20,          /* bInterval: Polling Interval (32 ms) */
    /* 34 */
        
    0x07,   /* bLength: Endpoint Descriptor size */
    USB_ENDPOINT_DESCRIPTOR_TYPE,   /* bDescriptorType: */
        /*  Endpoint descriptor type */
    0x01,   /* bEndpointAddress: */
        /*  Endpoint Address (OUT) */
    0x03,   /* bmAttributes: Interrupt endpoint */
    0x02,   /* wMaxPacketSize: 2 Bytes max  */
    0x00,
    0x20,   /* bInterval: Polling Interval (20 ms) */
    /* 41 */    
        
}; /* CustomHID_ConfigDescriptor */
const uint8_t CustomHID_ReportDescriptor[CUSTOMHID_SIZ_REPORT_DESC] =
{                    
    0x05,0x01,// Global Generic Desktop
    0x09,0x06,// Local KeyBoard 
    0xA1,0x01,// Main app collection
    0x05,0x07,// Global KeyBoard
    //////////////////////////////////?1??
    0x19,0xe0,// Local Usage Min (KeyBoard LeftControl)
    0x29,0xe7,// Local Usage Max (KeyBoard Right GUI)
    0x15,0x00,// Global Logical Min
    0x25,0x01,// Global Logical Max 
    0x95,0x08,// Global ReportCount
    0x75,0x01,// Global ReportSize
    0x81,0x02,// Main Input(Data,Var,Abs)
    //////////////////////////////////?2??
    0x95,0x01,// Global ReportCount
    0x75,0x08,// Global ReportSize
    0x81,0x03,// Main Input(Cnst,Var,Abs)
    //////////////////////////////////?3-8??
    0x95,0x06,// Global ReportCount
    0x75,0x08,// Global ReportSize
    0x15,0x00,// Global Logical Min
    0x26,0xff,0x00,//Global Logical Max
    0x19,0x00,// Local Usage Min
    0x29,0x65,// Local Usage Max
    0x81,0x00,// Main Output(Data,Ary,Abs)
    ////////////////////////////////1??????
    0x15,0x00,// Global Logical Min
    0x25,0x01,// Global Logical Max
    0x95,0x05,// Global ReportCount
    0x75,0x01,// Global ReportSize
    0x05,0x08,// Global LED
    0x19,0x01,// Local Usage Min
    0x29,0x05,// Local Usage Max
    0x91,0x02,// Main Output(Data,Var,Abs)
    ////////////////////////////////??????1???
    0x95,0x01,// Global ReportCount
    0x75,0x03,// Global ReportSize
    0x91,0x03,// Main Output(Cnst,Var,Abs)
    0xc0 // Main End collection
}; /* CustomHID_ReportDescriptor */

/* USB String Descriptors (optional) */
const uint8_t CustomHID_StringLangID[CUSTOMHID_SIZ_STRING_LANGID] =
{
        CUSTOMHID_SIZ_STRING_LANGID,
        USB_STRING_DESCRIPTOR_TYPE,
        0x09,
        0x04
}; /* LangID = 0x0409: U.S. English */

const uint8_t CustomHID_StringVendor[CUSTOMHID_SIZ_STRING_VENDOR] = {
    CUSTOMHID_SIZ_STRING_VENDOR, /* Size of Vendor string */
    USB_STRING_DESCRIPTOR_TYPE,  /* bDescriptorType*/
    /* Manufacturer: "NATIONS" */
    'N',
    0,
    'A',
    0,
    'T',
    0,
    'I',
    0,
    'O',
    0,
    'N',
    0,
    'S',
    0};

const uint8_t CustomHID_StringProduct[CUSTOMHID_SIZ_STRING_PRODUCT] = {CUSTOMHID_SIZ_STRING_PRODUCT, /* bLength */
                                                                       USB_STRING_DESCRIPTOR_TYPE, /* bDescriptorType */
                                                                       'N',
                                                                       0,
                                                                       '3',
                                                                       0,
                                                                       '2',
                                                                       0,
                                                                       'G',
                                                                       0,
                                                                       '4',
                                                                       0,
                                                                       '5',
                                                                       0,
                                                                       'x',
                                                                       0,
                                                                       'C',
                                                                       0,
                                                                       'u',
                                                                       0,
                                                                       's',
                                                                       0,
                                                                       't',
                                                                       0,
                                                                       'm',
                                                                       0,
                                                                       ' ',
                                                                       0,
                                                                       'H',
                                                                       0,
                                                                       'I',
                                                                       0,
                                                                       'D',
                                                                       0,
                                                                       '&',
                                                                       0,
                                                                       'S', 0, 'p', 0, 'e', 0, 'a', 0, 'k', 0, 'e', 0, 'r', 0
                                                                       };
uint8_t CustomHID_StringSerial[CUSTOMHID_SIZ_STRING_SERIAL]         = {CUSTOMHID_SIZ_STRING_SERIAL, /* bLength */
                                                               USB_STRING_DESCRIPTOR_TYPE, /* bDescriptorType */
                                                               'N',
                                                               0,
                                                               '3',
                                                               0,
                                                               '2',
                                                               0,
                                                               'g',
                                                               0,
                                                               '4',
                                                               0,
                                                               '5',
                                                               0,
                                                               'x',
                                                               0};


uint8_t Receive_Buffer[2];
u8 key_buffer[8] ={0};
u8* Ep1DataPtr;

uint8_t Stream_Buff[2048];
uint16_t In_Data_Offset = 0;
/* Private function prototypes -----------------------------------------------*/
/* Private functions ---------------------------------------------------------*/
#define BUFSZ   1024
#define SOUND_DEVICE_NAME    "sound0"    /* Audio 设备名称 */
static rt_device_t snd_dev;              /* Audio 设备句柄 */

/**
 * @brief  EP1 OUT Callback Routine./
 */
void EP1_OUT_Callback(void)
{
    uint8_t data_length=0;
    /* Read received data */  
    data_length = USB_SilRead(EP1_OUT, Receive_Buffer);
  
    if(data_length == 1)
    {

    }
    /* Enable the receive of data on EP1 */
    SetEPRxStatus(ENDP1, EP_RX_VALID);
}

/**
 * @brief  EP1 IN Callback Routine.
 */
void EP1_IN_Callback(void)
{
    _SetEPTxStatus(ENDP1, EP_TX_NAK);
    // Ep1DataPtr       = key_buffer;
    // USB_SilWrite(EP1_IN, Ep1DataPtr, 8);
    // _SetEPTxStatus(ENDP1, EP_TX_VALID);
}

/* Extern variables ----------------------------------------------------------*/
/* Private function prototypes -----------------------------------------------*/
/* Extern function prototypes ------------------------------------------------*/
/* Private functions ---------------------------------------------------------*/

/**
 * @brief Endpoint 2 out callback routine.
 */
void EP2_OUT_Callback(void)
{
    uint16_t Data_Len;       /* data length*/

    if (_GetENDPOINT(ENDP2) & EP_DATTOG_TX)
    {
        /*read from ENDP1_BUF0Addr buffer*/
        Data_Len = _GetEPDblBuf0Count(ENDP2);
        USB_CopyPMAToUserBuf(Stream_Buff, ENDP2_BUF0Addr, Data_Len);
    }
    else
    {
        /*read from ENDP1_BUF1Addr buffer*/
        Data_Len = _GetEPDblBuf1Count(ENDP2);
        USB_CopyPMAToUserBuf(Stream_Buff, ENDP2_BUF1Addr, Data_Len);
    }
    USB_FreeUserBuf(ENDP2, EP_DBUF_OUT);
    In_Data_Offset += Data_Len;
    if(In_Data_Offset >= 640 || Data_Len < 16){
        rt_event_send(&audio_event, EVENT_AUDIO_WRITE);
    }
}


__IO uint16_t wIstr;            /* STS register last read value */
__IO uint8_t bIntPackSOF   = 0; /* SOFs received between 2 consecutive packets */
__IO uint32_t esof_counter = 0; /* expected SOF counter */
__IO uint32_t wCNTR        = 0;

/* function pointers to non-control endpoints service routines */
void (*pEpInt_IN[7])(void) = {
    EP1_IN_Callback,
    EP2_IN_Callback,
    EP3_IN_Callback,
    EP4_IN_Callback,
    EP5_IN_Callback,
    EP6_IN_Callback,
    EP7_IN_Callback,
};

void (*pEpInt_OUT[7])(void) = {
    EP1_OUT_Callback,
    EP2_OUT_Callback,
    EP3_OUT_Callback,
    EP4_OUT_Callback,
    EP5_OUT_Callback,
    EP6_OUT_Callback,
    EP7_OUT_Callback,
};

/**
 * @brief  STS events interrupt service routine
 */
void USB_Istr(void)
{
    __IO uint32_t EP[8];
    
    wIstr = _GetISTR();

#if (IMR_MSK & STS_CTRS)
    if (wIstr & STS_CTRS & wInterrupt_Mask)
    {
        /* servicing of the endpoint correct transfer interrupt */
        /* clear of the CTR flag into the sub */
        USB_CorrectTransferLp();
#ifdef CTR_CALLBACK
        CTR_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_RST)
    if (wIstr & STS_RST & wInterrupt_Mask)
    {
        _SetISTR((uint16_t)CLR_RST);
        Device_Property.Reset();
#ifdef RESET_CALLBACK
        RESET_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_DOVR)
    if (wIstr & STS_DOVR & wInterrupt_Mask)
    {
        _SetISTR((uint16_t)CLR_DOVR);
#ifdef DOVR_CALLBACK
        DOVR_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_ERROR)
    if (wIstr & STS_ERROR & wInterrupt_Mask)
    {
        _SetISTR((uint16_t)CLR_ERROR);
#ifdef ERR_CALLBACK
        ERR_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_WKUP)
    if (wIstr & STS_WKUP & wInterrupt_Mask)
    {
        _SetISTR((uint16_t)CLR_WKUP);
        Resume(RESUME_EXTERNAL);
#ifdef WKUP_CALLBACK
        WKUP_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_SUSPD)
    if (wIstr & STS_SUSPD & wInterrupt_Mask)
    {
        /* check if SUSPEND is possible */
        if (fSuspendEnabled)
        {
            Suspend();
        }
        else
        {
            /* if not possible then resume after xx ms */
            Resume(RESUME_LATER);
        }
        /* clear of the STS bit must be done after setting of CTRL_FSUSPD */
        _SetISTR((uint16_t)CLR_SUSPD);
#ifdef SUSP_CALLBACK
        SUSP_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_SOF)
    if (wIstr & STS_SOF & wInterrupt_Mask)
    {
        _SetISTR((uint16_t)CLR_SOF);
        bIntPackSOF++;

#ifdef SOF_CALLBACK
        SOF_Callback();
#endif
    }
#endif
    /*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*-*/
#if (IMR_MSK & STS_ESOF)
    uint32_t i=0;
    if (wIstr & STS_ESOF & wInterrupt_Mask)
    {
        /* clear ESOF flag in STS */
        _SetISTR((uint16_t)CLR_ESOF);

        if ((_GetFNR() & FN_RXDP) != 0)
        {
            /* increment ESOF counter */
            esof_counter++;

            /* test if we enter in ESOF more than 3 times with FSUSP =0 and RXDP =1=>> possible missing SUSP flag*/
            if ((esof_counter > 3) && ((_GetCNTR() & CTRL_FSUSPD) == 0))
            {
                /* this a sequence to apply a force RESET*/

                /*Store CTRL value */
                wCNTR = _GetCNTR();

                /*Store endpoints registers status */
                for (i = 0; i < 8; i++)
                    EP[i] = _GetENDPOINT(i);

                /*apply FRES */
                wCNTR |= CTRL_FRST;
                _SetCNTR(wCNTR);

                /*clear FRES*/
                wCNTR &= ~CTRL_FRST;
                _SetCNTR(wCNTR);

                /* poll for RESET flag in STS, this bit will be set by hardware, if this flag is always not set, it means maybe USB module failure */
                while ((_GetISTR() & STS_RST) == 0);

                /* clear RESET flag in STS */
                _SetISTR((uint16_t)CLR_RST);

                /*restore Enpoints*/
                for (i = 0; i < 8; i++)
                    _SetENDPOINT(i, EP[i]);

                esof_counter = 0;
            }
        }
        else
        {
            esof_counter = 0;
        }

        /* resume handling timing is made with ESOFs */
        Resume(RESUME_ESOF); /* request without change of the machine state */

#ifdef ESOF_CALLBACK
        ESOF_Callback();
#endif
    }
#endif
} /* USB_Istr */




/* Extern function prototypes ------------------------------------------------*/

/**
 * @brief   PowerOn
 */
USB_Result PowerOn(void)
{
    uint16_t wRegVal;

    /*** CNTR_PWDN = 0 ***/
    wRegVal = CTRL_FRST;
    _SetCNTR(wRegVal);

    /*** CTRL_FRST = 0 ***/
    wInterrupt_Mask = 0;
    _SetCNTR(wInterrupt_Mask);
    /*** Clear pending interrupts ***/
    _SetISTR(0);
    /*** Set interrupt mask ***/
    wInterrupt_Mask = CTRL_RSTM | CTRL_SUSPDM | CTRL_WKUPM;
    _SetCNTR(wInterrupt_Mask);

    return Success;
}

/**
 * @brief   handles switch-off conditions
 */
USB_Result PowerOff()
{
    /* disable all interrupts and force USB reset */
    _SetCNTR(CTRL_FRST);
    /* clear interrupt status register */
    _SetISTR(0);
    /* switch-off device */
    _SetCNTR(CTRL_FRST + CTRL_PD);
    /* sw variables reset */
    /* ... */

    return Success;
}
#ifdef USB_LOW_PWR_MGMT_SUPPORT
/**
* @brief  Configures system clock after wake-up from STOP: enable HSE, PLL
*         and select PLL as system clock source.
*/
void SYSCLKConfig_STOP(uint32_t RCC_PLLMULL)
{
    __IO uint32_t StartUpCounter = 0, HSEStatus = 0;

    /* SYSCLK, HCLK, PCLK2 and PCLK1 configuration ---------------------------*/
    /* Enable HSE */
    RCC->CTRL |= ((uint32_t)RCC_CTRL_HSEEN);
    /* Wait till HSE is ready and if Time out is reached exit */
    do
    {
        HSEStatus = RCC->CTRL & RCC_CTRL_HSERDF;
        StartUpCounter++;
    } while ((HSEStatus == 0) && (StartUpCounter != HSE_STARTUP_TIMEOUT));
    if ((RCC->CTRL & RCC_CTRL_HSERDF) != RESET)
    {
        HSEStatus = (uint32_t)0x01;
    }
    else
    {
        HSEStatus = (uint32_t)0x00;
    }

    if (HSEStatus == (uint32_t)0x01)
    {
        /* Enable Prefetch Buffer */
        FLASH->AC |= FLASH_AC_PRFTBFEN;

        /* Flash 2 wait state */
        FLASH->AC &= (uint32_t)((uint32_t)~FLASH_AC_LATENCY);
        FLASH->AC |= (uint32_t)FLASH_AC_LATENCY_4;
        /* HCLK = SYSCLK */
        RCC->CFG |= (uint32_t)RCC_CFG_AHBPRES_DIV1;
        /* PCLK2 = HCLK */
        RCC->CFG |= (uint32_t)RCC_CFG_APB2PRES_DIV2; // RCC_CFG_APB2PRES_DIV1
        /* PCLK1 = HCLK */
        RCC->CFG |= (uint32_t)RCC_CFG_APB1PRES_DIV4; // RCC_CFG_APB1PRES_DIV2
        /*  PLL configuration: PLLCLK = HSE * 18 = 144 MHz */
        RCC->CFG &= (uint32_t)((uint32_t) ~(RCC_CFG_PLLSRC | RCC_CFG_PLLHSEPRES | RCC_CFG_PLLMULFCT));
        RCC->CFG |= (uint32_t)(RCC_CFG_PLLSRC_HSE | RCC_PLLMULL);
        /* Enable PLL */
        RCC->CTRL |= RCC_CTRL_PLLEN;
        /* Wait till PLL is ready */
        while ((RCC->CTRL & RCC_CTRL_PLLRDF) == 0)
        {
            /* if this flag is always not set, it means PLL is failed, User can add here some code to deal with this error*/
        }
        /* Select PLL as system clock source */
        RCC->CFG &= (uint32_t)((uint32_t) ~(RCC_CFG_SCLKSW));
        RCC->CFG |= (uint32_t)RCC_CFG_SCLKSW_PLL;
        /* Wait till PLL is used as system clock source */
        while ((RCC->CFG & (uint32_t)RCC_CFG_SCLKSTS) != (uint32_t)0x08)
        {
            /* if this bits are always not set, it means system clock select PLL failed, User can add here some code to deal with this error*/
        }
    }
    else
    { /* If HSE fails to start-up, the application will have wrong clock
        configuration. User can add here some code to deal with this error */
    }
}
#endif  /* USB_LOW_PWR_MGMT_SUPPORT */

/*******************************************************************************
 * Function Name  : Suspend
 * Description    : sets suspend mode operating conditions
 * Input          : None.
 * Output         : None.
 * Return         : Success.
 *******************************************************************************/
void Suspend(void)
{
    uint32_t i = 0;
    uint16_t wCNTR;
        
    /* suspend preparation */
    /* ... */

    /*Store CTRL value */
    wCNTR = _GetCNTR();

    /* This a sequence to apply a force RESET to handle a robustness case */

    /*Store endpoints registers status */
    for (i = 0; i < 8; i++)
        EP[i] = _GetENDPOINT(i);

    /* unmask RESET flag */
    wCNTR |= CTRL_RSTM;
    _SetCNTR(wCNTR);

    /*apply FRES */
    wCNTR |= CTRL_FRST;
    _SetCNTR(wCNTR);

    /*clear FRES*/
    wCNTR &= ~CTRL_FRST;
    _SetCNTR(wCNTR);

    /* poll for RESET flag in STS, this bit will be set by hardware, if this flag is always not set, it means maybe USB module failure */
    while ((_GetISTR() & STS_RST) == 0);

    /* clear RESET flag in STS */
    _SetISTR((uint16_t)CLR_RST);

    /*restore Enpoints*/
    for (i = 0; i < 8; i++)
        _SetENDPOINT(i, EP[i]);

    /* Now it is safe to enter macrocell in suspend mode */
    wCNTR |= CTRL_FSUSPD;
    _SetCNTR(wCNTR);

    /* force low-power mode in the macrocell */
    wCNTR = _GetCNTR();
    wCNTR |= CTRL_LP_MODE;
    _SetCNTR(wCNTR);

#ifdef USB_LOW_PWR_MGMT_SUPPORT
    PWR_EnterSTOP2Mode(PWR_STOPENTRY_WFI);
    SYSCLKConfig_STOP(RCC_CFG_PLLMULFCT18);
#endif  /* USB_LOW_PWR_MGMT_SUPPORT */
}

/*******************************************************************************
 * Function Name  : Resume_Init
 * Description    : Handles wake-up restoring normal operations
 * Input          : None.
 * Output         : None.
 * Return         : Success.
 *******************************************************************************/
void Resume_Init(void)
{
    uint16_t wCNTR;

    /* ------------------ ONLY WITH BUS-POWERED DEVICES ---------------------- */
    /* restart the clocks */
    /* ...  */

    /* CTRL_LP_MODE = 0 */
    wCNTR = _GetCNTR();
    wCNTR &= (~CTRL_LP_MODE);
    _SetCNTR(wCNTR);

#ifdef USB_LOW_PWR_MGMT_SUPPORT      
    /* restore full power */
    /* ... on connected devices */
    Leave_LowPowerMode();
#endif /* USB_LOW_PWR_MGMT_SUPPORT */

    /* reset FSUSP bit */
    _SetCNTR(IMR_MSK);

    /* reverse suspend preparation */
    /* ... */
}

/**
 * @brief   This is the state machine handling resume operations and
 *                 timing sequence. The control is based on the Resume structure
 *                 variables and on the ESOF interrupt calling this subroutine
 *                 without changing machine state.
 * @param   eResumeSetVal：RESUME_ESOF doesn't change ResumeS.eState allowing
 *                  decrementing of the ESOF counter in different states.
 */
void Resume(RESUME_STATE eResumeSetVal)
{
    uint16_t wCNTR;

    if (eResumeSetVal != RESUME_ESOF)
        ResumeS.eState = eResumeSetVal;
    switch (ResumeS.eState)
    {
    case RESUME_EXTERNAL:
        if (remotewakeupon == 0)
        {
            Resume_Init();
            ResumeS.eState = RESUME_OFF;
        }
        else /* RESUME detected during the RemoteWAkeup signalling => keep RemoteWakeup handling*/
        {
            ResumeS.eState = RESUME_ON;
        }
        break;
    case RESUME_INTERNAL:
        Resume_Init();
        ResumeS.eState = RESUME_START;
        remotewakeupon = 1;
        break;
    case RESUME_LATER:
        ResumeS.bESOFcnt = 2;
        ResumeS.eState   = RESUME_WAIT;
        break;
    case RESUME_WAIT:
        ResumeS.bESOFcnt--;
        if (ResumeS.bESOFcnt == 0)
            ResumeS.eState = RESUME_START;
        break;
    case RESUME_START:
        wCNTR = _GetCNTR();
        wCNTR |= CTRL_RESUM;
        _SetCNTR(wCNTR);
        ResumeS.eState   = RESUME_ON;
        ResumeS.bESOFcnt = 10;
        break;
    case RESUME_ON:
        ResumeS.bESOFcnt--;
        if (ResumeS.bESOFcnt == 0)
        {
            wCNTR = _GetCNTR();
            wCNTR &= (~CTRL_RESUM);
            _SetCNTR(wCNTR);
            ResumeS.eState = RESUME_OFF;
            remotewakeupon = 0;
        }
        break;
    case RESUME_OFF:
    case RESUME_ESOF:
    default:
        ResumeS.eState = RESUME_OFF;
        break;
    }
}

uint32_t ProtocolValue;
__IO uint8_t EXTI_Enable;
__IO uint8_t Request = 0;
uint8_t Report_Buf[2];   
uint32_t MUTE_DATA = 0;
/* -------------------------------------------------------------------------- */
/*  Structures initializations */
/* -------------------------------------------------------------------------- */

USB_Device Device_Table = {EP_NUM, 1};

DEVICE_PROP Device_Property = {
    CustomHID_init,
    CustomHID_Reset,
    CustomHID_Status_In,
    CustomHID_Status_Out,
    CustomHID_Data_Setup,
    CustomHID_NoData_Setup,
    CustomHID_Get_Interface_Setting,
    CustomHID_GetDeviceDescriptor,
    CustomHID_GetConfigDescriptor,
    CustomHID_GetStringDescriptor,
    0,
    0x40 /*MAX PACKET SIZE*/
};
USER_STANDARD_REQUESTS User_Standard_Requests = {CustomHID_GetConfiguration,
                                                 CustomHID_SetConfiguration,
                                                 CustomHID_GetInterface,
                                                 CustomHID_SetInterface,
                                                 CustomHID_GetStatus,
                                                 CustomHID_ClearFeature,
                                                 CustomHID_SetEndPointFeature,
                                                 CustomHID_SetDeviceFeature,
                                                 CustomHID_SetDeviceAddress};

USB_OneDescriptor Device_Descriptor = {(uint8_t*)CustomHID_DeviceDescriptor, CUSTOMHID_SIZ_DEVICE_DESC};

USB_OneDescriptor Config_Descriptor = {(uint8_t*)CustomHID_ConfigDescriptor, CUSTOMHID_SIZ_CONFIG_DESC};

USB_OneDescriptor CustomHID_Report_Descriptor = {(uint8_t*)CustomHID_ReportDescriptor, CUSTOMHID_SIZ_REPORT_DESC};

USB_OneDescriptor CustomHID_Hid_Descriptor = {(uint8_t*)CustomHID_ConfigDescriptor + CUSTOMHID_OFF_HID_DESC,
                                              CUSTOMHID_SIZ_HID_DESC};

USB_OneDescriptor String_Descriptor[4] = {{(uint8_t*)CustomHID_StringLangID, CUSTOMHID_SIZ_STRING_LANGID},
                                          {(uint8_t*)CustomHID_StringVendor, CUSTOMHID_SIZ_STRING_VENDOR},
                                          {(uint8_t*)CustomHID_StringProduct, CUSTOMHID_SIZ_STRING_PRODUCT},
                                          {(uint8_t*)CustomHID_StringSerial, CUSTOMHID_SIZ_STRING_SERIAL}};

/*CustomHID_SetReport_Feature function prototypes*/
uint8_t* CustomHID_SetReport_Feature(uint16_t Length);

/* Extern function prototypes ------------------------------------------------*/
                                                                                    
/**
 * @brief  Custom HID init routine.
 */                                                                                 
void CustomHID_init(void)
{
    /* Update the serial number string descriptor with the data from the unique
    ID*/
    pInformation->CurrentConfiguration = 0;
    /* Connect the device */
    PowerOn();

    /* Perform basic device initialization operations */
    USB_SilInit();

    bDeviceState = UNCONNECTED;
}

/**
 * @brief  Custom HID reset routine.
 */ 
void CustomHID_Reset(void)
{
    /* Set CustomHID_DEVICE as not configured */
    pInformation->CurrentConfiguration = 0;
    pInformation->CurrentInterface     = 0; /*the default Interface*/

    /* Current Feature initialization */
    pInformation->CurrentFeature = CustomHID_ConfigDescriptor[7];

    USB_SetBuftab(BTABLE_ADDRESS);

    /* Initialize Endpoint 0 */
    USB_SetEpType(ENDP0, EP_CONTROL);
    SetEPTxStatus(ENDP0, EP_TX_STALL);
    USB_SetEpRxAddr(ENDP0, ENDP0_RXADDR);
    USB_SetEpTxAddr(ENDP0, ENDP0_TXADDR);
    USB_ClrStsOut(ENDP0);
    USB_SetEpRxCnt(ENDP0, Device_Property.MaxPacketSize);
    USB_SetEpRxValid(ENDP0);

    /* Initialize Endpoint 1 */
    USB_SetEpType(ENDP1, EP_INTERRUPT);
    USB_SetEpTxAddr(ENDP1, ENDP1_TXADDR);
    USB_SetEpRxAddr(ENDP1, ENDP1_RXADDR);
    USB_SetEpTxCnt(ENDP1, 8);
    USB_SetEpRxCnt(ENDP1, 2);
    // SetEPRxStatus(ENDP1, EP_RX_VALID);
    // SetEPTxStatus(ENDP1, EP_TX_VALID);
    
    /* Initialize Endpoint 2 */
    USB_SetEpType(ENDP2, EP_ISOCHRONOUS);
    USB_SetEpDblBuferAddr(ENDP2, ENDP2_BUF0Addr, ENDP2_BUF1Addr);
    USB_SetEpDblBuferCnt(ENDP2, EP_DBUF_OUT, 0x40);
    USB_ClrDattogRx(ENDP2);
    USB_ClrDattogTx(ENDP2);
    USB_DattogTx(ENDP2);
    SetEPRxStatus(ENDP2, EP_RX_VALID);
    SetEPTxStatus(ENDP2, EP_TX_DIS);

    /* Set this device to response on default address */
    USB_SetDeviceAddress(0);
    bDeviceState = ATTACHED;
}

/**
 * @brief  Update the device state to configured and command the ADC
 *                  conversion.
 */ 
void CustomHID_SetConfiguration(void)
{
    if (pInformation->CurrentConfiguration != 0)
    {
        /* Device configured */
        bDeviceState = CONFIGURED;
    }
}

/**
 * @brief  Update the device state to addressed.
 */ 
void CustomHID_SetDeviceAddress(void)
{
    bDeviceState = ADDRESSED;
}

/**
 * @brief  CustomHID status IN routine.
 */
void CustomHID_Status_In(void)
{
}

/**
 * @brief  CustomHID status OUT routine.
 */
void CustomHID_Status_Out(void)
{
}

/**
 * @brief  Handle the data class specific requests.
 * @param Request Nb.
 */
USB_Result CustomHID_Data_Setup(uint8_t RequestNo)
{
    uint8_t* (*CopyRoutine)(uint16_t);

    CopyRoutine = NULL;

    if ((pInformation->bmRequestType & REQUEST_TYPE) == STANDARD_REQUEST)
    {
        if (RequestNo == GET_DESCRIPTOR)
        {
            if (pInformation->USBwValue1 == REPORT_DESCRIPTOR)
            {
                CopyRoutine = CustomHID_GetReportDescriptor;
            }
            else if (pInformation->USBwValue1 == HID_DESCRIPTOR_TYPE)
            {
                CopyRoutine = CustomHID_GetHIDDescriptor;
            }

        }
    }
    /*** GET_PROTOCOL, GET_REPORT, SET_REPORT ***/
    else if ((pInformation->bmRequestType & REQUEST_TYPE) == CLASS_REQUEST)
    {
        switch (RequestNo)
        {
        case GET_PROTOCOL:
            CopyRoutine = CustomHID_GetProtocolValue;
            break;
        case SET_REPORT:
            CopyRoutine = CustomHID_SetReport_Feature;
            Request     = SET_REPORT;
            break;
        case SET_IDLE:
            CopyRoutine = CustomHID_SetIdle;
            break;
        case GET_CUR:
        case SET_CUR:
            CopyRoutine = Mute_Command;
            break;
        default:
            break;
        }
    }

    if (CopyRoutine == NULL)
    {
        return UnSupport;
    }

    pInformation->Ctrl_Info.CopyData    = CopyRoutine;
    pInformation->Ctrl_Info.Usb_wOffset = 0;
    (*CopyRoutine)(0);
    return Success;
}

/**
 * @brief  Set Feature request handling
 * @param Length.
 */
uint8_t* CustomHID_SetReport_Feature(uint16_t Length)
{
    if (Length == 0)
    {
        pInformation->Ctrl_Info.Usb_wLength = 2;
        return NULL;
    }
    else
    {
        return Report_Buf;
    }
}

/**
 * @brief   handle the no data class specific requests
 * @param   Request Nb.
 */
USB_Result CustomHID_NoData_Setup(uint8_t RequestNo)
{
    if ((Type_Recipient == (CLASS_REQUEST | INTERFACE_RECIPIENT)) && (RequestNo == SET_PROTOCOL))
    {
        return CustomHID_SetProtocol();
    }

    else
    {
        return UnSupport;
    }
}

/**
 * @brief   Gets the device descriptor.
 * @param   Length
 */
uint8_t* CustomHID_GetDeviceDescriptor(uint16_t Length)
{
    return Standard_GetDescriptorData(Length, &Device_Descriptor);
}

/**
 * @brief   Gets the configuration descriptor.
 * @param   Length
 */
uint8_t* CustomHID_GetConfigDescriptor(uint16_t Length)
{
    return Standard_GetDescriptorData(Length, &Config_Descriptor);
}

/**
 * @brief   Gets the string descriptors according to the needed index
 * @param   Length
 */
uint8_t* CustomHID_GetStringDescriptor(uint16_t Length)
{
    uint8_t wValue0 = pInformation->USBwValue0;
    if (wValue0 >= 4)
    {
        return NULL;
    }
    else
    {
        return Standard_GetDescriptorData(Length, &String_Descriptor[wValue0]);
    }
}

/**
 * @brief   Gets the HID report descriptor.
 * @param   Length
 */
uint8_t* CustomHID_GetReportDescriptor(uint16_t Length)
{
    return Standard_GetDescriptorData(Length, &CustomHID_Report_Descriptor);
}

/**
 * @brief   Gets the HID descriptor.
 * @param   Length
 */
uint8_t* CustomHID_GetHIDDescriptor(uint16_t Length)
{
    return Standard_GetDescriptorData(Length, &CustomHID_Hid_Descriptor);
}

/**
 * @brief   tests the interface and the alternate setting according to the
 *                  supported one.
 * @param   Interface : interface number.
 *          AlternateSetting : Alternate Setting number.
 */
USB_Result CustomHID_Get_Interface_Setting(uint8_t Interface, uint8_t AlternateSetting)
{
    if (AlternateSetting > 2)
    {
        return UnSupport;
    }
    else if (Interface > 2)
    {
        return UnSupport;
    }
    return Success;
}

/*******************************************************************************
 * Function Name  : CustomHID_SetReport_Feature
 * Description    : Set Feature request handling
 * Input          : Length.
 * Output         : None.
 * Return         : Buffer
 *******************************************************************************/
uint8_t* CustomHID_SetIdle(uint16_t Length)
{
    pInformation->Ctrl_Info.Usb_wLength = 0;
    return NULL;
}

/**
 * @brief   CustomHID Set Protocol request routine.
 */
USB_Result CustomHID_SetProtocol(void)
{
    uint8_t wValue0 = pInformation->USBwValue0;
    ProtocolValue   = wValue0;
    return Success;
}

/**
 * @brief   get the protocol value
 * @param   Length
 */
uint8_t* CustomHID_GetProtocolValue(uint16_t Length)
{
    if (Length == 0)
    {
        pInformation->Ctrl_Info.Usb_wLength = 1;
        return NULL;
    }
    else
    {
        return (uint8_t*)(&ProtocolValue);
    }
}

/**
 * @brief Handle the GET MUTE and SET MUTE command.
 * @param Length : data length.
 * @return MUTE data
 */
uint8_t *Mute_Command(uint16_t Length)
{
    if (Length == 0)
    {
        pInformation->Ctrl_Info.Usb_wLength = pInformation->wLengths.w;
        return NULL;
    }
    else
    {
        return((uint8_t*)(&MUTE_DATA));
    }
}

void USB_LP_CAN1_RX0_IRQHandler(void)
{
    rt_interrupt_enter();
    USB_Istr();
    rt_interrupt_leave();
}

/**
 * @brief  This function handles USB WakeUp interrupt request.
 */
void USBWakeUp_IRQHandler(void)
{
    rt_interrupt_enter();
    EXTI_ClrITPendBit(EXTI_LINE18);
    rt_interrupt_leave();
}
void audio_thread_entry(void *param)
{
    int ret = RT_EOK;
    rt_uint32_t e;
    while (1) {
        ret = rt_event_recv(&audio_event, ( EVENT_AUDIO_WRITE) ,
                            RT_EVENT_FLAG_OR | RT_EVENT_FLAG_CLEAR,
                            RT_WAITING_FOREVER, &e);
        if (ret != RT_EOK)
        {
            rt_kprintf("thread1: OR recv ret 0x%x event 0x%x\n", ret, e);
        }
        if (e & EVENT_AUDIO_OPEN) {
            /* 根据设备名称查找 Audio 设备，获取设备句柄 */
            if (snd_dev == RT_NULL) {
                snd_dev = rt_device_find(SOUND_DEVICE_NAME);
                if (snd_dev == RT_NULL) {
                    rt_kprintf("find %s failed\r\n", SOUND_DEVICE_NAME);
                }
            }
            if(snd_dev != RT_NULL)
            {
                /* 以只写方式打开 Audio 播放设备 */
                int ret = rt_device_open(snd_dev, RT_DEVICE_OFLAG_WRONLY);
                if(ret != RT_EOK){
                    rt_kprintf("open %s failed\r\n", SOUND_DEVICE_NAME);
                }
            }
        }
        static int x = 0;
        if (e & EVENT_AUDIO_WRITE) {
            if (In_Data_Offset > 0) {
                /* 播放音频数据 */
                x += In_Data_Offset;
                // rt_device_write(snd_dev, 0, Stream_Buff, In_Data_Offset);
                rt_kprintf("audio write %d\r\n", x);
                // LOG_HEX("audio write", 8, Stream_Buff, In_Data_Offset);
                In_Data_Offset = 0;
            }
        }
        if (e & EVENT_AUDIO_CLOSE) {
            if(snd_dev != RT_NULL)
            {
                /* 以只写方式打开 Audio 播放设备 */
                int ret = rt_device_close(snd_dev);
                if(ret != RT_EOK){
                    rt_kprintf("close %s failed\r\n", SOUND_DEVICE_NAME);
                }
            }
        }
    }
}
void audio_thread_init(void)
{
    /* 初始化事件对象 */
    int result;
    result = rt_event_init(&audio_event, "audio_event", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK)
    {
        rt_kprintf("init event failed.\n");
        return ;
    }
    
    rt_thread_t audio_thread = rt_thread_create("audio_thread", audio_thread_entry, RT_NULL, 1024, 5, 10);
    if (audio_thread != RT_NULL) {
        int ret = rt_thread_startup(audio_thread);
        if(ret != RT_EOK){
            rt_kprintf("audio thread startup failed %d\r\n", ret);
        }
    }

}
void audio_o(void)
{
    rt_event_send(&audio_event, EVENT_AUDIO_OPEN);

}
#endif

#endif