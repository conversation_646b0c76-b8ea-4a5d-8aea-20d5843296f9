
#include <rtthread.h>
#include "lds_uart.h"
#define DBG_TAG "ldsUart"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

/**
 * @brief UART receive message structure
 * @details Structure used for passing UART receive information through message queue
 */
struct rx_msg {
    rt_device_t dev;         /**< RT-Thread device handle */
    rt_size_t size;          /**< Size of received data */
};
/* 消息队列控制块 */
static struct rt_messagequeue rx_mq;

static ldsUartCb_t ldsUartCbs[LDS_UART_INDEX_MAX] = {0};

/* 接收数据回调函数 */
static rt_err_t ldsUartIsrCallback(rt_device_t dev, rt_size_t size)
{
    struct rx_msg msg;
    rt_err_t result;
    msg.dev = dev;
    msg.size = size;

    result = rt_mq_send(&rx_mq, &msg, sizeof(msg));
    if ( result == -RT_EFULL)
    {
        /* 消息队列满 */
        rt_kprintf("%s got mq full!,size %d\n", dev->parent.name, size);
    }
    return result;
}
/**
 * @brief Register callback function for UART interface
 * @details Registers or updates the callback function for the specified UART interface
 *
 * @param index UART interface index from LDS_UART_INDEX_E
 * @param cb Callback function to handle received data
 * @return int Return code (0 for success, -1 for invalid index)
 *
 * @note This function can be used to change the callback after initialization
 * @note Passing NULL as cb will unregister the callback
 *
 * @example
 * @code
 * // Register a new callback for UART index 2
 * int result = ldsUartCbRegister(LDS_UART_INDEX_2, newCallbackFunction);
 * if (result != 0) {
 *     rt_kprintf("Failed to register callback\n");
 * }
 * @endcode
 */
int ldsUartCbRegister(LDS_UART_INDEX_E index, ldsUartCb_t cb)
{
    if(index >= LDS_UART_INDEX_MAX){
        LOG_E("Invalid uart index: %d", index);
        return -1;
    }
    if(cb == NULL){
        LOG_W("Unregister callback for uart index: %d", index);
        return -1;
    }
    if(ldsUartCbs[index] != NULL){
        LOG_E("Callback already registered for uart index: %d", index);
        return -1;
    }
    ldsUartCbs[index] = cb;
    return 0;
}
static void ldsUartCmdProcess(rt_device_t dev, const uint8_t* data, rt_size_t size)
{
    if(dev->device_id >= LDS_UART_INDEX_MAX){
        LOG_E("Invalid uart index: %d", dev->device_id);
        return;
    }
    if(ldsUartCbs[dev->device_id] == NULL){
        LOG_E("No callback registered for uart index: %d", dev->device_id);
        return;
    }
    ldsUartCbs[dev->device_id](dev, data, size);
}
static void ldsUartThread(void *parameter)
{
    struct rx_msg msg;
    rt_err_t result;
    int32_t rx_length;
    static uint8_t rx_buffer[RT_SERIAL_RB_BUFSZ];

    while (1)
    {
        rt_memset(&msg, 0, sizeof(msg));
        /* 从消息队列中读取消息*/
        result = rt_mq_recv(&rx_mq, &msg, sizeof(msg), RT_WAITING_FOREVER);
        if (result > 0)
        {
            /* 从串口读取数据*/
            rx_length = rt_device_read(msg.dev, 0, rx_buffer, msg.size);
            // rt_kprintf(rx_length <= 0 ? "read nothing!\n" : "read %d bytes\n", rx_length);
            if(rx_length < 0){
                LOG_E("Read from %s failed, %d", msg.dev->parent.name, rx_length);
                continue;
            }
            ldsUartCmdProcess(msg.dev, rx_buffer, rx_length);
            /* 打印数据 */
            // rt_kprintf("%s\n",rx_buffer);
        }
    }
}
/**
 * @brief Internal UART system initialization
 * @details Initializes the UART message queue and processing thread
 *          This function is called automatically by ldsUartInit()
 *
 * @return rt_err_t RT-Thread error code
 * @note This function is for internal use and should not be called directly
 */
rt_err_t __ldsUartInit(void)
{
    static char msg_pool[128];
    static rt_thread_t thread = RT_NULL;
    rt_err_t ret = RT_EOK;

    if(thread != RT_NULL){
        return ret;
    }
    /* 初始化消息队列 */
    ret = rt_mq_init(&rx_mq, "rx_mq",
                    msg_pool,                 /* 存放消息的缓冲区 */
                    sizeof(struct rx_msg),    /* 一条消息的最大长度 */
                    sizeof(msg_pool),         /* 存放消息的缓冲区大小 */
                    RT_IPC_FLAG_FIFO);        /* 如果有多个线程等待，按照先来先得到的方法分配消息 */
    if(ret != RT_EOK){
        LOG_E("Init lds_uart mq failed %d", ret);
        return ret;
    }
    /* 创建 serial 线程 */
    thread = rt_thread_create("serial", ldsUartThread, RT_NULL, 2048, 6, 10);
    /* 创建成功则启动线程 */
    if (thread != RT_NULL)
    {
        ret = rt_thread_startup(thread);
        if(ret != RT_EOK){
            LOG_E("Thread lds_uart startup failed %d", ret);
            rt_thread_delete(thread);
            thread = RT_NULL;
        }
    }
    else
    {
        ret = RT_ERROR;
        LOG_E("Create thread lds_uart failed");
    }
    return ret;
}

rt_device_t ldsUartInit(const char* uart_name, LDS_UART_INDEX_E index, ldsUartCb_t cb)
{
    /* 用于接收消息的信号量 */
    rt_device_t serial;

    if(__ldsUartInit()) {
        return RT_NULL;
    }
    /* 查找串口设备 */
    serial = rt_device_find(uart_name);
    if (!serial)
    {
        rt_kprintf("find %s failed!\n", uart_name);
        return RT_NULL;
    }

    /* 以 DMA 接收及轮询发送方式打开串口设备 */
    rt_device_open(serial, RT_DEVICE_FLAG_RDWR | RT_DEVICE_FLAG_DMA_RX);
    /* 设置接收回调函数 */
    rt_device_set_rx_indicate(serial, ldsUartIsrCallback);
    serial->device_id = index;
    ldsUartCbRegister(index, cb);
    return serial;
}
