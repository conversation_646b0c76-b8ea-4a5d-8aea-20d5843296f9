/**
 * @file lds_dsp.c
 * @brief LDS dsp Communication Protocol Stack Implementation
 * @details This file implements a complete communication protocol stack for dsp devices
 *          following the specified protocol format with big-endian byte order.
 *          This version includes a command queue for robust multi-command handling.
 * <AUTHOR> Team
 * @version 1.0
 * @date 2025-07-23
 *
 * @copyright Copyright (c) 2025 LDS
 *
 * Protocol Format:
 * Head(2) + CTRL(1) + LEN(1) + DATA(variable) + End(1)
 * - Small-endian byte order (LSB first)
 * - Head: Fixed 0xA55A for all frames
 * - CTRL: Control area code defining the operation
 * - LEN: Length of DATA field in bytes
 * - DATA: Variable length payload data
 * - End: Fixed 0x16 for all frames
 */

#include <rtdevice.h>
#include <stdlib.h>
#include "lds_utils.h"
#include "lds_uart.h"
#include "lds_dsp.h"
#include "lds_led_config.h"
#include "lds_uac.h"

#define DBG_TAG "DSP"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define DSP_VERSION_MAX_LEN     16

#define DSP_EFFECT_VOLUME_MAX     0
#define DSP_EFFECT_VOLUME_MIN     -9
#define DSP_EFFECT_BASS_MAX       5
#define DSP_EFFECT_BASS_MIN       -4
#define DSP_EFFECT_TREBLE_MAX     5
#define DSP_EFFECT_TREBLE_MIN     -4
#define DSP_EFFECT_MUTE_REG       0x01
#define DSP_EFFECT_VOLUME_REG     0x02
#define DSP_EFFECT_BASS_REG       0x07
#define DSP_EFFECT_TREBLE_REG     0x0C
/* ================================ Configuration ========================== */
#define DSP_SERIAL_NAME         "uart6"
#define DSP_POWER_CTRL_PIN      "PB.0"
#define DSP_CMD_QUEUE_SIZE      16                           /**< Command queue size, supporting up to 16 pending commands */
#define DSP_MAX_ERROR_COUNT     10                           /**< Maximum consecutive errors before asxore device is not connected */
#define DSP_LINE_IN_MULTIPLIER_HIGH 5
#define DSP_LINE_IN_MULTIPLIER_LOW  0
/* ================================ Timeout Configuration ================== */
#define DSP_RESPONSE_TIMEOUT    (RT_TICK_PER_SECOND * 2)    /**< 2 seconds response timeout */
#define DSP_HEARTBEAT_TIMEOUT   (RT_TICK_PER_SECOND * 20)   /**< 20 seconds heartbeat timeout */
#define DSP_PARSE_TIMEOUT       (RT_TICK_PER_SECOND * 1)   /**< 1s parse timeout for state machine */
#define DSP_RETRY_COUNT         2                           /**< Maximum retry attempts */

/* ================================ Global Variables ======================= */

static rt_base_t g_dspPowerCtrl = -1;       /**< dsp power control pin */
static rt_device_t g_dspDev = RT_NULL;       /**< UART device handle */
static struct rt_timer g_heartbeatTimer;    /**< Heartbeat timeout timer */
static struct rt_timer g_retransmissionTimer; /**< Retransmission timer for command queue */
static struct rt_timer g_parseTimer;        /**< Parse timeout timer for state machine */
static struct rt_mutex g_dspMutex;          /**< Thread safety mutex */
static uint8_t g_errorCount = 0 ;       /**< Error count for consecutive errors */

/* ================================ Protocol State Machine ================ */
static lds_dsp_frame_t g_rxFrame;           /**< Current receiving frame */
static uint8_t g_rxBuffer[DSP_MAX_FRAME_LEN]; /**< Frame receive buffer */
static uint8_t g_rxIndex = 0;              /**< Current receive index */

/* ================================ Command Queue Management ================ */
/**
 * @brief Pending command queue entry structure
 * @details Contains all information for a command awaiting ACK, including retry management.
 */
typedef struct {
    bool active;                            /**< Defines if this queue slot is in use */
    uint8_t ctrl;                           /**< Control code */
    uint8_t dataLen;                       /**< Length of the data payload */
    uint8_t data[DSP_MAX_DATA_LEN];         /**< Data payload */
    uint8_t retryCount;                     /**< Current retry count */
    rt_tick_t sent_timestamp;               /**< System tick when the command was last sent */
} lds_dsp_cmd_queue_entry_t;

static lds_dsp_cmd_queue_entry_t g_cmdQueue[DSP_CMD_QUEUE_SIZE]; /**< Command queue */
static void ldsDspStartRetransmissionTimer(void);
static int ldsDspSendFrame(uint8_t ctrl, const uint8_t *data, uint8_t dataLen);
static void ldsDspResetStateMachine(void);

/**
 * @brief dsp protocol state enumeration
 * @details Defines the states for the protocol frame parsing state machine
 */
typedef enum {
    DSP_STATE_IDLE = 0,        /**< Waiting for frame header */
    DSP_STATE_HEAD_1,          /**< Receiving head 1 0xA5 */
    DSP_STATE_HEAD_2,          /**< Receiving head 2 0x5A */
    DSP_STATE_CMD,             /**< Receiving command high byte */
    DSP_STATE_LEN,             /**< Receiving length byte */
    DSP_STATE_DATA,            /**< Receiving data payload */
    DSP_STATE_END,             /**< Receiving CRC16 high byte */
} LDS_DSP_STATE_E;

static LDS_DSP_STATE_E g_rxState = DSP_STATE_IDLE;

/**
 * @brief Reset dsp device
 * @details Performs hardware reset of the dsp device via power control pin
 */
static void ldsDspReset(void)
{
    if (g_dspPowerCtrl <= 0) {
        LOG_E("DSP power control pin not initialized");
        return;
    }

    LOG_I("Resetting dsp");
    rt_pin_write(g_dspPowerCtrl, PIN_LOW);
    rt_thread_mdelay(500);
    rt_pin_write(g_dspPowerCtrl, PIN_HIGH);
    rt_thread_mdelay(100);
}

/**
 * @brief Heartbeat timeout handler
 * @details Called when heartbeat timeout occurs, triggers device reset
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsDspHeartbeatTimeout(void *parameter)
{
    LOG_W("heartbeat timeout, resetting device");
    ldsDspReset();
}

/**
 * @brief Parse timeout handler
 * @details Called when parse timeout occurs, resets the state machine to prevent hanging
 *
 * @param parameter Timer parameter (unused)
 */
static void ldsDspParseTimeout(void *parameter)
{
    LOG_W("Parse timeout state %d, reset", g_rxState);
    ldsDspResetStateMachine();
}

/**
 * @brief Initialize the command queue
 * @details Clears all entries in the command queue, marking them as inactive.
 */
static void ldsDspInitCmdQueue(void)
{
    rt_memset(&g_cmdQueue, 0, sizeof(g_cmdQueue));
}

/**
 * @brief Find the oldest pending command in the queue.
 * @details Iterates through the queue to find the active command with the earliest
 *          sent timestamp. This command is considered the "head" for retransmission.
 * @return Pointer to the oldest command entry, or RT_NULL if the queue is empty.
 */
static lds_dsp_cmd_queue_entry_t* ldsDspFindOldestCmd(void)
{
    lds_dsp_cmd_queue_entry_t *oldest_cmd = RT_NULL;
    rt_tick_t min_timestamp = RT_TICK_MAX;

    for (int i = 0; i < DSP_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            if (oldest_cmd == RT_NULL || (g_cmdQueue[i].sent_timestamp < min_timestamp)) {
                min_timestamp = g_cmdQueue[i].sent_timestamp;
                oldest_cmd = &g_cmdQueue[i];
            }
        }
    }
    return oldest_cmd;
}

/**
 * @brief Retransmission timeout handler.
 * @details This function is called when the retransmission timer expires. It handles
 *          the retransmission or dropping of the oldest command in the queue.
 * @param parameter Unused.
 */
static void ldsDspRetransmissionTimeout(void *parameter)
{
    rt_mutex_take(&g_dspMutex, RT_WAITING_FOREVER);

    lds_dsp_cmd_queue_entry_t *cmd_to_retry = ldsDspFindOldestCmd();

    if (cmd_to_retry != RT_NULL) {
        if (cmd_to_retry->retryCount >= DSP_RETRY_COUNT) {
            LOG_E("Max retries for cmd=0x%02X, Dropping.", cmd_to_retry->ctrl);
            cmd_to_retry->active = false; // Drop the command
            g_errorCount ++;
            if(g_errorCount > DSP_MAX_ERROR_COUNT){
                LOG_W("DSP reach max error count,most likely not connected");
            }
        } else {
            cmd_to_retry->retryCount++;
            LOG_W("Retrying cmd 0x%02X,attempt %d/%d",
                  cmd_to_retry->ctrl, cmd_to_retry->retryCount, DSP_RETRY_COUNT);

            // Resend the command with the same sequence number
            ldsDspSendFrame(cmd_to_retry->ctrl, cmd_to_retry->data, cmd_to_retry->dataLen);
            cmd_to_retry->sent_timestamp = rt_tick_get();
        }
    }

    // After handling, always try to restart the timer for the next pending command
    ldsDspStartRetransmissionTimer();

    rt_mutex_release(&g_dspMutex);
}

/**
 * @brief Starts the retransmission timer if there are pending commands.
 * @details Finds the oldest command and sets a one-shot timer for it.
 *          This function must be called with the mutex held.
 */
static void ldsDspStartRetransmissionTimer(void)
{
    rt_timer_stop(&g_retransmissionTimer);

    lds_dsp_cmd_queue_entry_t *next_cmd = ldsDspFindOldestCmd();
    if (next_cmd != RT_NULL) {
        rt_tick_t timeout_tick = DSP_RESPONSE_TIMEOUT;
        // Optional: Can calculate remaining time if needed, but a fixed timeout is simpler.
        rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_SET_TIME, &timeout_tick);
        rt_timer_start(&g_retransmissionTimer);
    }
}

/**
 * @brief Send protocol frame
 * @details Constructs and sends a complete protocol frame with CRC16.
 *          Note: This is the low-level send function and does not manage the queue.
 *
 * @param ctrl Control code (big-endian)
 * @param data Pointer to data payload (can be NULL if dataLen is 0)
 * @param dataLen Length of data payload
 * @return int 0 on success, negative error code on failure
 */
static int ldsDspSendFrame(uint8_t ctrl, const uint8_t *data, uint8_t dataLen)
{
    uint8_t frame[DSP_MAX_FRAME_LEN];
    uint8_t frameLen;
    rt_size_t written;
    
    if (g_dspDev == RT_NULL) {
        LOG_E("DSP device not initialized");
        return -RT_ERROR;
    }

    if (dataLen > DSP_MAX_DATA_LEN) {
        LOG_E("Data length %d exceeds maximum %d", dataLen, DSP_MAX_DATA_LEN);
        return -RT_EINVAL;
    }

    /* Construct frame */
    frameLen = 0;
    frame[frameLen++] = DSP_FRAME_HEAD1;          /* Head1 */
    frame[frameLen++] = DSP_FRAME_HEAD2;          /* Head2 */
    frame[frameLen++] = ctrl;                      /* CMD */
    frame[frameLen++] = dataLen;                 /* LEN  byte */

    /* Copy data payload */
    if (data != RT_NULL && dataLen > 0) {
        rt_memcpy(&frame[frameLen], data, dataLen);
        frameLen += dataLen;
    }

    frame[frameLen++] = DSP_FRAME_END;            /* Frame end */

    /* Send frame */
    written = rt_device_write(g_dspDev, 0, frame, frameLen);
    if (written != frameLen) {
        LOG_E("Failed to send complete frame, sent %d of %d bytes", written, frameLen);
        return -RT_ERROR;
    }

    LOG_D("Sent frame:  cmd=0x%02X, len=%d", ctrl, dataLen);
    // LOG_HEX("dsp-tx", 16, frame, frameLen);

    return 0; 
}

/**
 * @brief Sends a command and adds it to the pending queue.
 * @details This is the new main function for sending commands. It finds a free
 *          slot in the queue, sends the frame, and manages the retransmission timer.
 * @param ctrl Control code
 * @param data Pointer to data payload
 * @param dataLen Length of data payload
 * @return 0 on success, negative error code on failure.
 */
static int ldsDspSendCommand(uint8_t ctrl, const uint8_t *data, uint8_t dataLen)
{
    rt_err_t result = rt_mutex_take(&g_dspMutex, RT_WAITING_FOREVER);
    if (result != RT_EOK) {
        LOG_E("Failed to acquire mutex: %d", result);
        return -RT_ERROR;
    }

    int free_slot_idx = -1;
    for (int i = 0; i < DSP_CMD_QUEUE_SIZE; i++) {
        if (!g_cmdQueue[i].active) {
            free_slot_idx = i;
            break;
        }
    }

    if (free_slot_idx == -1) {
        LOG_E("Command queue is full. Cannot send cmd 0x%04X.", ctrl);
        rt_mutex_release(&g_dspMutex);
        return -RT_EBUSY;
    }

    // Backup current seq, as ldsDspSendFrame will modify it.
    int ret = ldsDspSendFrame(ctrl, data, dataLen);

    if (ret < 0) {
        rt_mutex_release(&g_dspMutex);
        return ret; // Propagate error
    }

    // Populate queue entry
    lds_dsp_cmd_queue_entry_t *entry = &g_cmdQueue[free_slot_idx];
    entry->active = true;
    entry->ctrl = ctrl;
    entry->dataLen = dataLen;
    if (dataLen > 0) {
        rt_memcpy(entry->data, data, dataLen);
    }
    entry->retryCount = 0;
    entry->sent_timestamp = rt_tick_get();

    // LOG_D("Ctrl 0x%02X added to queue.", ctrl);

    // If the timer is not running (i.e., queue was empty), start it.
    rt_uint8_t timer_state = 0;
    rt_timer_control(&g_retransmissionTimer, RT_TIMER_CTRL_GET_STATE, &timer_state);
    if (timer_state == RT_TIMER_FLAG_DEACTIVATED) {
        ldsDspStartRetransmissionTimer();
    }

    rt_mutex_release(&g_dspMutex);
    return 0;
}
static bool ldsDspAckCheck(uint8_t ctrl)
{
    /* Reset heartbeat timer on any valid frame */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_start(&g_heartbeatTimer);

    g_errorCount = 0;
    // This is an ACK or a response frame. Try to match it with a pending command.
    rt_mutex_take(&g_dspMutex, RT_WAITING_FOREVER);
    bool ack_matched = false;
    for (int i = 0; i < DSP_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active && g_cmdQueue[i].ctrl == ctrl) {
            LOG_D("ACK received for cmd=%02X. Removing from queue.", ctrl);
            g_cmdQueue[i].active = false; // Deactivate the command
            ack_matched = true;
            // The command was acknowledged. We need to check if we should restart the timer
            // for the next oldest command.
            ldsDspStartRetransmissionTimer();
            break;
        }
    }
    if (!ack_matched) {
        LOG_W("Received ACK for unexpected command type 0x%02X",  ctrl);
    }
    rt_mutex_release(&g_dspMutex);
    return ack_matched;
}
/**
 * @brief Process received protocol frame
 * @details Handles complete received frames and dispatches based on command type
 *
 * @param frame Pointer to received frame structure
 * @return int 0 on success, negative error code on failure
 */
static int ldsDspProcessFrame(const lds_dsp_frame_t *frame)
{
    static bool version_flag = false;
    if (frame == RT_NULL) {
        LOG_E("Invalid frame pointer");
        return -RT_EINVAL;
    }

    LOG_D("Processing frame: cmd=0x%02X,len=%d", frame->ctrl,  frame->dataLen);

    ldsDspAckCheck(frame->ctrl);

    switch (frame->ctrl) {
        case LDS_DSP_EFFECT_CTRL_VERSION:
            if(!version_flag){
                version_flag = true;
                LOG_D("version :%d.%d.%d, effect version %d.%d.%d robo version %d.%d.%d", frame->data[1],frame->data[2],frame->data[3],
                    frame->data[4],frame->data[5],frame->data[6], frame->data[7],frame->data[8],frame->data[9]);
            }
            break;            
        default:
            LOG_W("Unknown command received: 0x%04X", frame->ctrl);
            return -RT_ERROR;
    }
    return 0;
}

/**
 * @brief Start parse timeout timer
 * @details Starts or restarts the parse timeout timer to prevent state machine hanging
 */
static void ldsDspStartParseTimer(void)
{
    rt_timer_stop(&g_parseTimer);
    rt_timer_start(&g_parseTimer);
}

/**
 * @brief Reset frame parsing state machine
 * @details Resets the state machine to idle state and clears buffers
 */
static void ldsDspResetStateMachine(void)
{
    /* Stop parse timeout timer */
    rt_timer_stop(&g_parseTimer);

    g_rxState = DSP_STATE_IDLE;
    g_rxIndex = 0;
    rt_memset(&g_rxFrame, 0, sizeof(g_rxFrame));
    rt_memset(g_rxBuffer, 0, sizeof(g_rxBuffer));
}
static bool ldsDspIsHeadData(const uint8_t *data, rt_size_t size, rt_size_t index)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return false;
    }
    if(index + 1 >= size){
        LOG_D("index %d + 1 >= size %d", index, size);
        return false;
    }
    if ((data[index] == DSP_FRAME_HEAD1) && (data[index + 1] == DSP_FRAME_HEAD2)) {
        return true;
    }
    return false;
}
/**
 * @brief Protocol frame parsing state machine
 * @details Parses incoming bytes according to the protocol specification
 *
 * @param data Pointer to received data buffer
 * @param size Size of received data
 * @return int 0 on success, negative error code on failure
 */
static int ldsDspParseData(const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    // LOG_HEX("dsp-Rx", 16, data, size);

    for (rt_size_t i = 0; i < size; i++) {
        uint8_t byte = data[i];
        // LOG_D("state %d", g_rxState);
        switch (g_rxState) {
            case DSP_STATE_IDLE:
                if(!ldsDspIsHeadData(data, size, i) && ldsDspAckCheck(byte)){
                    ldsDspResetStateMachine();
                    break;
                }
                if (byte == DSP_FRAME_HEAD1) {
                    ldsDspResetStateMachine();
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head1 = byte;
                    g_rxState = DSP_STATE_HEAD_1;
                    /* Start parse timeout timer when entering parsing state */
                    ldsDspStartParseTimer();
                }  
                break;

            case DSP_STATE_HEAD_1:
                if (byte == DSP_FRAME_HEAD2) {
                    g_rxBuffer[g_rxIndex++] = byte;
                    g_rxFrame.head2 = byte;
                    g_rxState = DSP_STATE_CMD;
                    /* Start parse timeout timer when entering parsing state */
                    ldsDspStartParseTimer();
                }
                break;

            case DSP_STATE_CMD:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.ctrl = byte;
                g_rxState = DSP_STATE_LEN;
                ldsDspStartParseTimer();
                break;

            case DSP_STATE_LEN:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.dataLen = byte;

                /* Validate data length */
                if (g_rxFrame.dataLen > DSP_MAX_DATA_LEN) {
                    LOG_E("Invalid data length: %d", g_rxFrame.dataLen);
                    ldsDspResetStateMachine();
                    break;
                }

                if (g_rxFrame.dataLen == 0) {
                    g_rxState = DSP_STATE_END;
                } else {
                    g_rxState = DSP_STATE_DATA;
                }
                rt_memset(g_rxFrame.data, 0, sizeof(g_rxFrame.data));
                ldsDspStartParseTimer();
                break;

            case DSP_STATE_DATA:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.data[g_rxIndex - 5] = byte;  /* Data starts at index 5 */

                if (g_rxIndex >= (4 + g_rxFrame.dataLen)) {
                    g_rxState = DSP_STATE_END;
                }
                ldsDspStartParseTimer();
                break;

            case DSP_STATE_END:
                g_rxBuffer[g_rxIndex++] = byte;
                g_rxFrame.end = byte;

                if (g_rxFrame.end != DSP_FRAME_END) {
                    LOG_E("End byte mismatch: end=0x%02X, received=0x%02X",
                          DSP_FRAME_END, g_rxFrame.end);
                    LOG_HEX("dsp-rx-err", 16, g_rxBuffer, g_rxIndex);
                    ldsDspResetStateMachine();
                    break;
                }

                // LOG_HEX("dsp-rx", 16, g_rxBuffer, g_rxIndex);
                ldsDspProcessFrame(&g_rxFrame);
                ldsDspResetStateMachine();
                break;

            default:
                LOG_E("Invalid state: %d", g_rxState);
                ldsDspResetStateMachine();
                break;
        }

        /* Prevent buffer overflow */
        if (g_rxIndex >= DSP_MAX_FRAME_LEN) {
            LOG_E("Frame buffer overflow");
            ldsDspResetStateMachine();
            break;
        }
    }

    return 0;
}

/**
 * @brief UART data processing callback
 * @details Callback function registered with UART driver for data processing
 *
 * @param dev RT-Thread device handle
 * @param data Pointer to received data buffer
 * @param size Size of received data in bytes
 * @return int 0 on success, negative error code on failure
 */
int ldsDspProcess(rt_device_t dev, const uint8_t *data, rt_size_t size)
{
    if (data == RT_NULL || size == 0) {
        LOG_E("Invalid data or size");
        return -RT_EINVAL;
    }

    if (dev == RT_NULL) {
        LOG_E("Invalid device handle");
        return -RT_EINVAL;
    }

    LOG_D("Received %d bytes from %s", size, dev->parent.name);

    // No mutex here, parse happens in UART's context.
    // Mutex is used inside process/ack logic.
    int ret = ldsDspParseData(data, size);

    return ret;
}

/* ================================ Public API Functions =================== */

int ldsDspQueryVersion(void)
{
    return ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_VERSION, RT_NULL, 0);
}

int ldsDspSetVolume(uint8_t volume)
{
    int ret = 0;
    uint8_t data[3];
    int16_t volume_trans = 0;
    
    volume_trans = -9 + volume;
    if(volume_trans > DSP_EFFECT_VOLUME_MAX) {
        volume_trans = DSP_EFFECT_VOLUME_MAX;
    }
    if(volume_trans < DSP_EFFECT_VOLUME_MIN) {
        volume_trans = DSP_EFFECT_VOLUME_MIN;
    }

    volume_trans = volume_trans * 100;
    data[0] = DSP_EFFECT_VOLUME_REG;
    data[1] = (uint8_t)(volume_trans & 0xFF);
    data[2] = (uint8_t)(volume_trans >> 8);

    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_VOLUME1, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send volume1 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_VOLUME2, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send volume2 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_VOLUME3, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send volume3 cmd failed");
        return ret;
    }
    return 0;
}

int ldsDspSetTreble(uint8_t treble)
{   
    int ret = 0;
    uint8_t data[3];
    int8_t treble_trans = 0;

    treble_trans = treble - 4;
    if(treble_trans > DSP_EFFECT_TREBLE_MAX) {
        treble_trans = DSP_EFFECT_TREBLE_MAX;
    }
    if(treble_trans < DSP_EFFECT_TREBLE_MIN) {
        treble_trans = DSP_EFFECT_TREBLE_MIN;
    }
    data[0] = DSP_EFFECT_TREBLE_REG;
    data[1] = 0;
    data[2] = (uint8_t)treble_trans;

    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_TREBLE_BASS1, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send treble1 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_TREBLE_BASS2, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send treble2 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_TREBLE_BASS3, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send treble3 cmd failed");
        return ret;
    }
    return 0;
}

int ldsDspSetBass(uint8_t bass)
{
    int ret = 0;
    uint8_t data[3];
    int8_t bass_trans = 0;

    bass_trans = bass - 4;
    if(bass_trans > DSP_EFFECT_BASS_MAX) {
        bass_trans = DSP_EFFECT_BASS_MAX;
    }
    if(bass_trans < DSP_EFFECT_BASS_MIN) {
        bass_trans = DSP_EFFECT_BASS_MIN;
    }
    data[0] = DSP_EFFECT_BASS_REG;
    data[1] = 0;
    data[2] = (uint8_t)bass_trans;

    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_TREBLE_BASS1, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send bass1 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_TREBLE_BASS2, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send bass2 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_TREBLE_BASS3, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send bass3 cmd failed");
        return ret;
    }
    return 0;
}

int ldsDspSetLineInMultiplier(bool line_in1, bool high)
{
    uint8_t ctrl = line_in1 ? LDS_DSP_EFFECT_CTRL_LINE_IN1 : LDS_DSP_EFFECT_CTRL_LINE_IN2;
    uint8_t multi = high ? DSP_LINE_IN_MULTIPLIER_HIGH : DSP_LINE_IN_MULTIPLIER_LOW;
    uint8_t data[3];
    int16_t volume_trans = 0;
    LOG_D("Set line %d in multiplier to %d", line_in1 ? 1 : 2, multi);
    
    volume_trans = multi * 100;
    data[0] = DSP_EFFECT_VOLUME_REG;
    data[1] = (uint8_t)(volume_trans & 0xFF);
    data[2] = (uint8_t)(volume_trans >> 8);
    
    return ldsDspSendCommand(ctrl, data, sizeof(data));
}

int ldsDspSetLineSelect(bool on)
{
    int ret = 0;
    uint8_t data[3];
    data[0] = DSP_EFFECT_MUTE_REG;
    data[1] = on;
    data[2] = 0;
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_LINE_IN1, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send line in1 cmd failed");
        return ret;
    }
    ret = ldsDspSendCommand(LDS_DSP_EFFECT_CTRL_FREELESS_MIC, data, sizeof(data));
    if(ret < 0) {
        LOG_E("Send line in2 cmd failed");
        return ret;
    }
    return 0;
}
/**
 * @brief Initialize dsp communication system
 * @details Initializes hardware, UART communication, timers, and state machine
 *
 * @return int 0 on success, negative error code on failure
 *
 * @note This function performs complete dsp system initialization including:
 *       - Power control pin setup
 *       - UART interface initialization with callback
 *       - Timer configuration for heartbeat and retransmission
 *       - Mutex initialization for thread safety
 *       - State machine and command queue initialization
 */
int ldsDspInit(void)
{
    rt_err_t result;

    /* Initialize mutex for thread safety */
    result = rt_mutex_init(&g_dspMutex, "dsp_mutex", RT_IPC_FLAG_PRIO);
    if (result != RT_EOK) {
        LOG_E("Failed to initialize mutex: %d", result);
        return -RT_ERROR;
    }

    /* Initialize power control pin */
    g_dspPowerCtrl = power_ctrl_pin_init(DSP_POWER_CTRL_PIN, PIN_HIGH);
    if (g_dspPowerCtrl < 0) {
        LOG_E("Failed to initialize DSP power control pin %s", DSP_POWER_CTRL_PIN);
        rt_mutex_detach(&g_dspMutex);
        return -RT_ERROR;
    }
    /* Initialize UART with callback */
    g_dspDev = ldsUartInit(DSP_SERIAL_NAME, LDS_UART_INDEX_6, ldsDspProcess);
    if (g_dspDev == RT_NULL) {
        LOG_E("Failed to initialize DSP UART %s", DSP_SERIAL_NAME);
        rt_mutex_detach(&g_dspMutex);
        return -RT_ERROR;
    }

    /* Initialize heartbeat timer */
    rt_timer_init(&g_heartbeatTimer, "base_hb",
                  ldsDspHeartbeatTimeout,
                  RT_NULL,
                  DSP_HEARTBEAT_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_PERIODIC);
    rt_timer_start(&g_heartbeatTimer);

    /* Initialize retransmission timer */
    rt_timer_init(&g_retransmissionTimer, "base_retry",
                  ldsDspRetransmissionTimeout,
                  RT_NULL,
                  DSP_RESPONSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize parse timeout timer */
    rt_timer_init(&g_parseTimer, "base_parse",
                  ldsDspParseTimeout,
                  RT_NULL,
                  DSP_PARSE_TIMEOUT,
                  RT_TIMER_FLAG_SOFT_TIMER | RT_TIMER_FLAG_ONE_SHOT);

    /* Initialize state machine */
    ldsDspResetStateMachine();

    /* Initialize command queue */
    ldsDspInitCmdQueue();


    LOG_I("dsp communication system initialized successfully");
    return 0;
}

/**
 * @brief Deinitialize dsp communication system
 * @details Cleans up all resources and stops communication
 *
 * @return int 0 on success, negative error code on failure
 */
int ldsDspDeinit(void)
{
    /* Stop timers */
    rt_timer_stop(&g_heartbeatTimer);
    rt_timer_stop(&g_retransmissionTimer);
    rt_timer_stop(&g_parseTimer);
    rt_timer_detach(&g_heartbeatTimer);
    rt_timer_detach(&g_retransmissionTimer);
    rt_timer_detach(&g_parseTimer);

    /* Reset state machine */
    ldsDspResetStateMachine();
    
    /* Clear command queue */
    rt_mutex_take(&g_dspMutex, RT_WAITING_FOREVER);
    ldsDspInitCmdQueue();
    rt_mutex_release(&g_dspMutex);

    /* Close UART device */
    if (g_dspDev != RT_NULL) {
        rt_device_close(g_dspDev);
        g_dspDev = RT_NULL;
    }

    /* Power down device */
    if (g_dspPowerCtrl > 0) {
        rt_pin_write(g_dspPowerCtrl, PIN_LOW);
        g_dspPowerCtrl = -1;
    }

    /* Cleanup mutex */
    rt_mutex_detach(&g_dspMutex);

    LOG_I("dsp communication system deinitialized");
    return 0;
}

/* ================================ MSH Debug Commands ===================== */
#ifdef RT_USING_FINSH

static int ldsDspQueueStatus(void)
{
    rt_kprintf("Command Queue Status (Size: %d):\n", DSP_CMD_QUEUE_SIZE);
    bool empty = true;
    for (int i = 0; i < DSP_CMD_QUEUE_SIZE; i++) {
        if (g_cmdQueue[i].active) {
            empty = false;
            rt_kprintf("  Slot %d: [ACTIVE]\n", i);
            rt_kprintf("  cmd: 0x%04X,\n", g_cmdQueue[i].ctrl);
            rt_kprintf("    retries: %d, sent_at: %u\n",
                       g_cmdQueue[i].retryCount, g_cmdQueue[i].sent_timestamp);
        }
    }
    if (empty) {
        rt_kprintf("  Queue is empty.\n");
    }
    return 0;
}


/**
 * @brief MSH command for dsp operations
 * @details Provides command-line interface for testing dsp communication
 *
 * @param argc Argument count
 * @param argv Argument vector
 * @return int 0 on success, negative error code on failure
 */
static int ldsDspCmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: dsp <command> [args...]\n");
        rt_kprintf("Commands:\n");
        rt_kprintf("  init                    - Initialize dsp system\n");
        rt_kprintf("  deinit                  - Deinitialize dsp system\n");
        rt_kprintf("  reset                   - Reset dsp device\n");
        rt_kprintf("  version                 - Query version information\n");
        rt_kprintf("  status                  - Show system status and queue\n");
        rt_kprintf("  test_crc16              - Test CRC16 implementation\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "init") == 0) {
        int ret = ldsDspInit();
        rt_kprintf("dsp init %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "deinit") == 0) {
        int ret = ldsDspDeinit();
        rt_kprintf("dsp deinit %s\n", ret == 0 ? "success" : "failed");
        return ret;
    }

    if (rt_strcmp(argv[1], "reset") == 0) {
        ldsDspReset();
        rt_kprintf("dsp device reset\n");
        return 0;
    }

    if (rt_strcmp(argv[1], "status") == 0) {
        rt_mutex_take(&g_dspMutex, RT_WAITING_FOREVER);
        rt_kprintf("dsp System Status:\n");
        rt_kprintf("  Device: %s\n", g_dspDev ? "initialized" : "not initialized");
        rt_kprintf("  Power Control: %s\n", g_dspPowerCtrl > 0 ? "enabled" : "disabled");
        rt_kprintf("  RX State: %d\n", g_rxState);
        rt_kprintf("  RX Index: %d\n", g_rxIndex);

        /* Check parse timer state */
        rt_uint8_t parse_timer_state = 0;
        rt_timer_control(&g_parseTimer, RT_TIMER_CTRL_GET_STATE, &parse_timer_state);
        rt_kprintf("  Parse Timer: %s\n", parse_timer_state == RT_TIMER_FLAG_ACTIVATED ? "active" : "inactive");

        ldsDspQueueStatus();
        rt_mutex_release(&g_dspMutex);
        return 0;
    }

    rt_kprintf("Unknown command: %s\n", argv[1]);
    return -RT_EINVAL;
}

MSH_CMD_EXPORT_ALIAS(ldsDspCmd, dsp, dsp communication protocol commands);
#endif