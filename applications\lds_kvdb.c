/**
 * @file lds_kvdb.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2025-06-04
 * 
 * @copyright Copyright (c) 2025
 * 
 */
#include <stdio.h>
#include <board.h>

#ifdef PKG_USING_FLASHDB
#include <flashdb.h>

#define DBG_TAG "kvdb"
#include <rtdbg.h>

static uint32_t boot_count = 0;
static time_t boot_time[10] = {0, 1, 2, 3};
 /* default KV nodes */
static struct fdb_default_kv_node default_kv_table[] = {
        {"username", "armink", 0}, /* string KV */
        {"password", "123456", 0}, /* string KV */
        {"boot_count", &boot_count, sizeof(boot_count)}, /* int type KV */
        {"boot_time", &boot_time, sizeof(boot_time)},    /* int array type KV */
};
/* KVDB object */
static struct fdb_kvdb kvdb = { 0 };

static rt_mutex_t kvdb_mutex;
static void lock(fdb_db_t db)
{
    rt_mutex_take(kvdb_mutex, RT_WAITING_FOREVER);
}

static void unlock(fdb_db_t db)
{
    rt_mutex_release(kvdb_mutex);
}
int ldsKvdbSet(const char *key, const void *data, uint32_t len)
{
    struct fdb_blob blob;
    return fdb_kv_set_blob(&kvdb, key, fdb_blob_make(&blob, data, len));
}

int ldsKvdbGet(const char *key, const void *data, uint32_t len)
{
    struct fdb_blob blob;
    return fdb_kv_get_blob(&kvdb, key, fdb_blob_make(&blob, data, len));
}

int ldsKvdbInit(void)
{
    fdb_err_t err;
    struct fdb_default_kv default_kv;
    /* 创建一个动态互斥量 */
    kvdb_mutex = rt_mutex_create("kvdb_mutex", RT_IPC_FLAG_PRIO);
    if (kvdb_mutex == RT_NULL)
    {
        LOG_E("create dynamic mutex failed.\n");
        return -1;
    }

    default_kv.kvs = default_kv_table;
    default_kv.num = sizeof(default_kv_table) / sizeof(default_kv_table[0]);
    /* set the lock and unlock function if you want */
    fdb_kvdb_control(&kvdb, FDB_KVDB_CTRL_SET_LOCK, (void *)lock);
    fdb_kvdb_control(&kvdb, FDB_KVDB_CTRL_SET_UNLOCK, (void *)unlock);

    /* Key-Value database initialization
    *
    *       &kvdb: database object
    *       "env": database name
    * "fdb_kvdb1": The flash partition name base on FAL. Please make sure it's in FAL partition table.
    *              Please change to YOUR partition name.
    * &default_kv: The default KV nodes. It will auto add to KVDB when first initialize successfully.
    *        NULL: The user data if you need, now is empty.
    */
    err = fdb_kvdb_init(&kvdb, "kvdb", "db", &default_kv, NULL);
    if (err != FDB_NO_ERR)
    {
        LOG_E("KVDB init failed: %d", err);
        return -1;
    }

    LOG_I("KVDB initialized successfully.");
    /* get the "boot_count" KV value */
    /* the blob.saved.len is more than 0 when get the value successful */
    if (ldsKvdbGet("boot_count", &boot_count, sizeof(boot_count)) > 0) {
        LOG_I("get the 'boot_count' value is %d\n", boot_count);
    } else {
        LOG_E("get the 'boot_count' failed\n");
    }
    /* increase the boot count */
    boot_count ++;
    /* change the "boot_count" KV's value */
    // ldsKvdbSet("boot_count", &boot_count, sizeof(boot_count));
    LOG_I("set the 'boot_count' value to %d\n", boot_count);
    return 0;
}
#endif
