/**
 * @file lds_digital_tube.c
 * <AUTHOR> Team
 * @brief Digital tube display driver implementation
 * @version 1.1
 * @date 2025-07-15
 *
 * @copyright Copyright (c) 2025 LDS
 * 
 */

#include <rtthread.h>
#include <rtdevice.h>
#include "lds_ch455.h"

#define DBG_TAG "digTube"
#include <rtdbg.h>

/* Example configuration */
#define CH455_I2C_BUS_NAME    "i2c1"
#define CH455_I2C_ADDR        (0x40)

/* Global variables for examples */
static lds_ch455_handle_t g_ch455_handle = RT_NULL;

int ldsDigitalTubeSetDigit(uint16_t number)
{
    if (g_ch455_handle == RT_NULL) {
        LOG_E("CH455 not initialized");
        return -1;
    }
    int result = ldsCh455DisplayDigit(g_ch455_handle, 0, number & 0x0F, false);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to display number: %d on seg0", result);
        return result;
    }
    result = ldsCh455DisplayDigit(g_ch455_handle, 1, number >> 4, false);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to display number: %d", result);
    }
    
    return result;
}

int ldsDigitalTubeSetNumber(uint16_t number)
{
    if (g_ch455_handle == RT_NULL) {
        LOG_E("CH455 not initialized");
        return -1;
    }
    int result = ldsCh455DisplayNumber(g_ch455_handle, number);
    if (result != LDS_CH455_ERR_NONE) {
        LOG_E("Failed to display number: %d", result);
    }
    
    return result;
}
int ldsDigitalTubeInit(void)
{
    LOG_I("=== CH455 Basic Initialization ===");
    
    /* Configure CH455 device */
    lds_ch455_config_t config = {
        .i2c_bus_name = CH455_I2C_BUS_NAME,
        .i2c_addr = CH455_I2C_ADDR,
        .i2c_timeout_ms = LDS_CH455_I2C_TIMEOUT_MS,
        .i2c_retry_count = LDS_CH455_I2C_RETRY_COUNT,
        .mode = LDS_CH455_MODE_DISPLAY_ONLY,
        .brightness = 4,
        .scan_interval_ms = LDS_CH455_SCAN_INTERVAL_MS,
        .debounce_time_ms = LDS_CH455_DEBOUNCE_TIME_MS,
        .auto_scan_enable = false,
        .key_callback = RT_NULL,
        .user_data = RT_NULL,
    };
    
    /* Initialize device */
    g_ch455_handle = ldsCh455Init(&config);
    if (g_ch455_handle == RT_NULL) {
        LOG_E("Failed to initialize CH455 device");
        return -1;
    }
    
    LOG_I("CH455 device initialized successfully");
    
    return 0;
}