#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

#include <rtdevice.h>
#include <ulog.h>

#include "lds_multiple_pin_switch.h"


/**
 * @brief Gets the current state/position of a lds_multiple_pin switch
 *
 * @param hd Pointer to lds_multiple_pin switch handler structure
 * @return int8_t Current position of the lds_multiple_pin switch, negative value indicates error
 */
int8_t ldsMultiplePinSwitchGet(lds_multiple_pin_switch_t * hd)
{
    int8_t value = 0;
    rt_ssize_t ret = 0;
    if (hd == RT_NULL) {
        LOG_E("Invalid lds_multiple_pin switch handle");
        return -1;
    }
    if (hd->pin_count == 0) {
        LOG_E("No pins configured for lds_multiple_pin switch");
        return -1;
    }
    for (int i = 0; i < hd->pin_count; i++) {
        ret = rt_pin_read(hd->pins[i]);
        if (ret < 0) { 
            LOG_E("Error reading pin %d ret %d", hd->pins[i], ret);
            return -1;
        }
        value |= (ret << i);
    }
    value = (hd->active_high) ? value : ~value;
    value &= (1 << hd->pin_count) - 1; // Mask to the number of pins
    if (value < 0) {
        LOG_E("Error reading lds_multiple_pin switch pins");
        return -1;
    }

    return value;
}

/**
 * @brief Initializes a lds_multiple_pin switch interface
 *
 * @param hd Pointer to the lds_multiple_pin switch handle structure
 * @param pin_names String containing comma-separated pin names to initialize
 * @param pin_count Number of pins to initialize
 * @param active_high true if pins are active high, false if active low
 * @param mode Input mode for the pins (normal, pull-up, pull-down)
 * @return int 0 on success, negative error code on failure
 */
int ldsMultiplePinSwitchInit(lds_multiple_pin_switch_t *hd, const char *pin_names, uint8_t pin_count, bool active_high, LDS_MULTIPLE_PIN_INPUT_MODE_E mode)
{
    char *copy_str = RT_NULL;
    rt_base_t pins[7] = {0};
    if(pin_names == RT_NULL || *pin_names == '\0') {
        LOG_E("Invalid pin names string");
        return -1;
    }
    if (pin_count > 7) {
        LOG_E("Maximum 7 pins supported");
        return -1;
    }
    if (pin_count == 0) {
        LOG_E("No pins configured for lds_multiple_pin switch");
        return -1;
    }
    if (hd == RT_NULL) {
        LOG_E("Invalid lds_multiple_pin switch handle");
        return -1;
    }
    copy_str = rt_strdup(pin_names);
    if (copy_str == RT_NULL) {
        LOG_E("%s strdup failed", pin_names);
        return -1;
    }
    rt_memset(hd, 0, sizeof(lds_multiple_pin_switch_t));
    rt_memset(pins, 0, sizeof(pins));

    hd->active_high = active_high;
    
    char *saveptr = RT_NULL;
    char *token = strtok_r(copy_str, ",", &saveptr);
    for (int i = 0; i < pin_count && token != RT_NULL; i++) {
        pins[i] = rt_pin_get(token);
        if (pins[i] == -1) {
            LOG_E("Invalid pin name: %s", token);
            rt_free(copy_str);
            return -1;
        }
        token = strtok_r(RT_NULL, ",", &saveptr);
    }
    memcpy(hd->pins, pins, sizeof(rt_base_t) * pin_count);
    
    for (int i = 0; i < pin_count; i++) {
        if(mode == LDS_MULTIPLE_PIN_INPUT_PULL_UP) {
            rt_pin_mode(hd->pins[i], PIN_MODE_INPUT_PULLUP);
        } else if (mode == LDS_MULTIPLE_PIN_INPUT_PULL_DOWN) {
            rt_pin_mode(hd->pins[i], PIN_MODE_INPUT_PULLDOWN);
        } else {
            rt_pin_mode(hd->pins[i], PIN_MODE_INPUT);
        }
    }
    hd->pin_count = pin_count;
    rt_free(copy_str);
    return 0;
}

void hid_keyboard_test(bool pagedown);
int lds_multiple_pin_test(void)
{
    static lds_multiple_pin_switch_t my_switch;
    static bool init_flag = false;
    char pin_names[] = "PE.12,PE.11,PE.10,PE.9";
    if(!init_flag) {
        init_flag = true;
        int ret = ldsMultiplePinSwitchInit(&my_switch, pin_names, 4, false, LDS_MULTIPLE_PIN_INPUT_PULL_UP);
        if (ret != 0) {
            LOG_E("Failed to initialize lds_multiple_pin switch");
            return ret;
        }
    }
    
    int8_t position = ldsMultiplePinSwitchGet(&my_switch);
    if (position < 0) {
        LOG_E("Error reading lds_multiple_pin switch position");
        return position;
    }
    if(position != 0) {
        // LOG_I("Current lds_multiple_pin switch position: %d", position);
        // if(2 == position){
        //     hid_keyboard_test(true);
        // } else if(3 == position){
        //     hid_keyboard_test(false);
        // }
    }
    return 0;

    
}
