
#include "lds_led_config.h"

#define DBG_TAG "key_cfg"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

#define LED_PIN_STD         "PD.13" // Standard LED pin
#define LED_PIN_BOY         "PD.9" // Boy LED pin
#define LED_PIN_GIRL        "PD.11" // Girl LED pin
#define LED_PIN_MAIN_MUTE   "PD.15" // Main mute LED pin
#define LED_PIN_SUB_MUTE    "PC.7" // Sub mute LED pin

static int led_pins[LED_CONFIG_TOTAL] = {0};

int ldsLedGetConfigPin(LED_CONFIG_E index)
{
    if (index >= LED_CONFIG_TOTAL)
    {
        LOG_E("Invalid LED pin index: %d", index);
        return -1;
    }
    return led_pins[index];
}

static void ldsLedSetConfigPin(LED_CONFIG_E index, int pin)
{
    if (index >= LED_CONFIG_TOTAL)
    {
        LOG_E("Invalid LED pin index: %d", index);
        return;
    }
    led_pins[index] = pin;
}

void ldsLedInit(void)
{
    int led = 0;
    int ret = ldsLedBaseInit();
    if (ret != RT_EOK)
    {
        LOG_E("LED base init failed!");
        return;
    }
    //tmp to test LED functionality
    // int led_tmp = ldsLedAdd("PB.8", LDS_LED_LEVEL_HIGH);
    // if (led_tmp < 0)
    // {
    //     LOG_E("Failed to initialize LED on pin %s", "PB.8");
    //     return;
    // }
    // ldsLedBlink(led_tmp, 500, 500);
    /* Initialize the LED */
    led = ldsLedAdd(LED_PIN_STD, LDS_LED_LEVEL_HIGH);
    if(led < 0)
    {
        LOG_E("Failed to initialize LED on pin %s", LED_PIN_STD);
        return;
    }
    LOG_I("LED initialized on pin %s", LED_PIN_STD);
    ldsLedSetConfigPin(LED_STD, led);
    led = ldsLedAdd(LED_PIN_BOY, LDS_LED_LEVEL_HIGH);
    if(led < 0)
    {
        LOG_E("Failed to initialize LED on pin %s", LED_PIN_BOY);
        return;
    }
    LOG_I("LED initialized on pin %s", LED_PIN_BOY);
    ldsLedSetConfigPin(LED_BOY, led);
    led = ldsLedAdd(LED_PIN_GIRL, LDS_LED_LEVEL_HIGH);
    if(led < 0)
    {
        LOG_E("Failed to initialize LED on pin %s", LED_PIN_GIRL);
        return;
    }
    LOG_I("LED initialized on pin %s", LED_PIN_GIRL);
    ldsLedSetConfigPin(LED_GIRL, led);
    led = ldsLedAdd(LED_PIN_MAIN_MUTE, LDS_LED_LEVEL_HIGH);
    if(led < 0)
    {
        LOG_E("Failed to initialize LED on pin %s", LED_PIN_MAIN_MUTE);
        return;
    }
    LOG_I("LED initialized on pin %s", LED_PIN_MAIN_MUTE);
    ldsLedSetConfigPin(LED_MAIN_MUTE, led);
    led = ldsLedAdd(LED_PIN_SUB_MUTE, LDS_LED_LEVEL_HIGH);
    if(led < 0)
    {
        LOG_E("Failed to initialize LED on pin %s", LED_PIN_SUB_MUTE);
        return;
    }
    LOG_I("LED initialized on pin %s", LED_PIN_SUB_MUTE);
    ldsLedSetConfigPin(LED_SUB_MUTE, led);
}
