/**
 * @file lds_bk9532.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 此文件包含BK9532芯片控制的实现
 * @version 0.1
 * @date 2025-05-19
 *
 * @copyright Copyright (c) 2025
 *
 */

#include "lds_bk9532.h"

#include <string.h>
#include <stdlib.h>
#include <rtdevice.h>
#include <rtthread.h>

#define DBG_TAG "BK9532"
#include <rtdbg.h>

#define CHIP_DEV_BK9532  0x26 
#define BK9532_WAIT_TIME 10
#define BK9532_REG_VAL_NUM 54
#define BK9532_REG_FREQ_NUM 64
#define REG_VAL_CNT 4

#define BK9532_CE_PIN "PC.14"
#define BK9532_I2C_DEV_NAME "i2c1"
#define BK9532_I2C_ADDR CHIP_DEV_BK9532

/* I2C可靠性配置 */
#define LDS_BK9532_I2C_RETRY_COUNT_DEFAULT      3
#define LDS_BK9532_I2C_RETRY_DELAY_BASE_MS      2
#define LDS_BK9532_I2C_RETRY_DELAY_MAX_MS       50
#define LDS_BK9532_REINIT_FAILURE_THRESHOLD     5
#define LDS_BK9532_I2C_TIMEOUT_MS               100


void lds_bk9532_set_regvalue_default(void);
void lds_bk9532_set_freq(uint8_t u8SeqCh);
uint8_t lds_bk9532_ReadData(uint8_t* dat,uint8_t cnt,uint8_t regAddr);

static struct rt_i2c_bus_device* i2c_dev = RT_NULL;
static rt_base_t ce_pin = -1;

static uint8_t  USER_ID_BUF[4]={0x00,0x00,0x09,0x01};
static uint8_t  USER_ID_BUF_TEST[4]={0xFF,0x00,0x09,0x01};

lds_bk9532_status_st sBk9532RegStatus;
lds_bk9532_status_st sBk9532RegStatusLast;

/* I2C可靠性增强变量 */
static uint8_t g_bk9532I2c_consecutive_failures = 0;
static lds_bk9532_i2c_config_t g_bk9532I2cConfig = {
    .retry_count = LDS_BK9532_I2C_RETRY_COUNT_DEFAULT,
    .retry_delay_base_ms = LDS_BK9532_I2C_RETRY_DELAY_BASE_MS,
    .retry_delay_max_ms = LDS_BK9532_I2C_RETRY_DELAY_MAX_MS,
    .reinit_threshold = LDS_BK9532_REINIT_FAILURE_THRESHOLD,
    .timeout_ms = LDS_BK9532_I2C_TIMEOUT_MS
};

static bool DevIsRegMode(void)
{
    // 待办：工厂测试
    return false;
}
// 待办：接收设置
static uint8_t g_u8ChannelSeq = 0;
static int8_t g_u8SetFreqCnt = 0;
//uint8_t g_u8TestI2CCnt = 0;

static const uint8_t bk9532_reg_val[BK9532_REG_VAL_NUM][REG_VAL_CNT] =
{
    {0xDF, 0xFF, 0xFF, 0xF8},	//00 REG0
    {0x04, 0xD2, 0x80, 0x57},	//02 REG1,0x52 to 0xD2,140515
    {0x89, 0x90, 0xE0, 0x28},	//04 REG2,
    {0x24, 0x52, 0x06, 0x9F},	//06 REG3, 
    {0x52, 0x88, 0x00, 0x44},	//08 REG4,
    {0x00, 0x28, 0x03, 0x80},	//0A REG5,
    {0x5B, 0xED, 0xFB, 0x00},	//0C REG6,
    {0x1C, 0x2E, 0xC5, 0xAA},	//0E REG7, UBAND,150917;
    {0xEF, 0xF1, 0x19, 0x4C},	//10 REG8, UBAND,150917
    {0x88, 0x51, 0x13, 0xA2},	//12 REG9, update REG9[7]=0-->1,140414;
    {0x00, 0x6F, 0x00, 0x6F},	//14 REGA
    {0x1B, 0xD2, 0x58, 0x63},	//16 REGB
    {0x00, 0x00, 0x00, 0x08},	//18 REGC,
    {0x3A, 0x9B, 0x69, 0xD0},	//A REGD,          
    {0x00, 0x00, 0x00, 0x00}, //58 REG2C, 混响    14
    {0x00, 0x00, 0x00, 0xff}, //5A REG2D, 混响
    {0xf1, 0x28, 0xa0, 0x00}, //5C REG2E, 混响
    {0x00, 0x00, 0x2e, 0x91}, //5E REF2F, 混响    17
    {0x40, 0x40, 0x40, 0x40}, //60 REG30, 使能GPIO3,GPIO2,GPIO1,GPIO0第二功能    
    {0xC1, 0x06, 0x00, 0x00}, //62 REG31, 0xC1, 0x08, 0x00, 0x71-->0xC1, 0x06, 0x00, 0x00
    {0x20, 0xFF, 0x0F, 0x07}, //64 REG32, 0x20, 0xFF, 0x0F, 0x09-->0x20, 0xFF, 0x0F, 0x07
    {0x00, 0x90, 0x00, 0x80}, //66 REG33, 音频RSSI门槛设定。  
    {0xFF, 0xFF, 0x01, 0x0E}, //68 REG34, 被动啸叫              
    {0x09, 0x00, 0x00, 0x00}, //6A REG35, 被动啸叫              
    {0x0C, 0x60, 0x60, 0xDD}, //6C REG36, 关混响 I2S使能，MSB在前，主模式。
    {0x3e, 0x00, 0x98, 0x00}, //6E REG37,    
    {0x40, 0xD7, 0xD5, 0xF7}, //70 REG38,
    {0x00, 0x00, 0x00, 0x00}, //72 REG39,    
    {0x28, 0x02, 0x05, 0x64}, //74 REG3A,0x28, 0x02, 0x05, 0x64-->0x00,0x02,0x05,0x64
    {0x6D, 0x00, 0x08, 0x00}, //76 REG3B, 开PLC
    {0x00, 0x40, 0xFF, 0xDC}, //78 REG3C,
    {0x00, 0x00, 0x66, 0x29},//7A REG3D, 
    {0x1F, 0x55, 0x4F, 0xEE}, //7C REG3E,
    {0x8D, 0x7A, 0x00, 0x2F}, //7E REG3F, 0x8D, 0x7A, 0x00, 0x2F
    {0x43, 0x00, 0x00, 0x00}, //B2 REG59, 混响    34
    {0x00, 0x00, 0x00, 0x00}, //B4 REG5A,
    {0x00, 0x00, 0x00, 0x00}, //B6 REG5B,
    {0x2c, 0xd5, 0x00, 0x00}, //B8 REG5C, 混响
    {0x1F, 0xFF, 0x3F, 0xFF}, //BA REG5D，
    {0x00, 0x00, 0x0F, 0x00}, //BC REG5E,  
    {0x00, 0x08, 0x95, 0x32}, //E0 REG70, ---chip ID Read
    {0x18, 0xA4, 0x08, 0x10}, //E2 REG71,
    {0x00, 0x00, 0x00, 0x00}, //E4 REG72,
    {0x00, 0x00, 0x00, 0x08}, //E6 REG73,
    {0x00, 0x00, 0x00, 0x00}, //E8 REG74,
    {0x00, 0x00, 0x06, 0x29}, //EA REG75,
    {0x00, 0x00, 0xFB, 0x06}, //EC REG76,
    {0x00, 0x00, 0x00, 0x00}, //EE REG77,
    {0x00, 0x00, 0x00, 0x00}, //F0 REG78,
    {0x00, 0x00, 0x00, 0x00}, //F2 REG79,
    {0x00, 0x01, 0x00, 0x01}, //F4 REG7A,
    {0x3B, 0xE4, 0x07, 0x96}, //F6 REG7B,
    {0x0F, 0x86, 0x00, 0x74}, //F8 REG7C,
    {0x00, 0x32, 0xA8, 0xFF},	//FA REG7D,         53
};

//接收频点和寄存器0D之间的公式对应关系,f的单位是MHz,48K
//Reg_0x0D = (f+0.16384)*6*2^23/24.576
static const uint8_t bk9532_reg_freq[BK9532_REG_FREQ_NUM][REG_VAL_CNT] =
{
    {0x49,0x75,0x3E,0x2D}, //587.5
    {0x49,0x85,0x3E,0x2D}, //588
    {0x49,0x95,0x3E,0x2D}, //588.5
    {0x49,0xA5,0x3E,0x2D}, //589
    {0x49,0xB5,0x3E,0x2D}, //589.5
    {0x49,0xC5,0x3E,0x2D}, //590
    {0x49,0xD5,0x3E,0x2D}, //590.5
    {0x49,0xE5,0x3E,0x2D}, //591
    {0x49,0xF5,0x3E,0x2D}, //591.5
    {0x4A,0x05,0x3E,0x2D}, //592
    {0x4A,0x15,0x3E,0x2D}, //592.5
    {0x4A,0x25,0x3E,0x2D}, //593
    {0x4A,0x35,0x3E,0x2D}, //593.5
    {0x4A,0x45,0x3E,0x2D}, //594
    {0x4A,0x55,0x3E,0x2D}, //594.5
    {0x4A,0x65,0x3E,0x2D}, //595
    {0x4A,0x75,0x3E,0x2D}, //595.5
    {0x4A,0x85,0x3E,0x2D}, //596
    {0x4A,0x95,0x3E,0x2D}, //596.5
    {0x4A,0xA5,0x3E,0x2D}, //597
    {0x4D,0x15,0x3E,0x2D}, //616.5
    {0x4A,0xC5,0x3E,0x2D}, //598
    {0x49,0xB5,0x3E,0x2D}, //589.5
    {0x4A,0xE5,0x3E,0x2D}, //599
    {0x4A,0xF5,0x3E,0x2D}, //599.5
    {0x4B,0x05,0x3E,0x2D}, //600
    {0x4B,0x25,0x3E,0x2D}, //601
    {0x4B,0x35,0x3E,0x2D}, //601.5
    {0x4B,0x45,0x3E,0x2D}, //602
    {0x4B,0x55,0x3E,0x2D}, //602.5
    {0x4B,0x75,0x3E,0x2D}, //603.5
    {0x4B,0x85,0x3E,0x2D}, //604
    {0x4B,0x95,0x3E,0x2D}, //604.5
    {0x4B,0xB5,0x3E,0x2D}, //605.5
    {0x4B,0xC5,0x3E,0x2D}, //606
    {0x4B,0xD5,0x3E,0x2D}, //606.5
    {0x4B,0xE5,0x3E,0x2D}, //607
    {0x4B,0xF5,0x3E,0x2D}, //607.5
    {0x4C,0x15,0x3E,0x2D}, //608.5
    {0x4C,0x25,0x3E,0x2D}, //609
    {0x4C,0x35,0x3E,0x2D}, //609.5
    {0x4C,0x45,0x3E,0x2D}, //610
    {0x4C,0x55,0x3E,0x2D}, //610.5
    {0x4C,0x75,0x3E,0x2D}, //611.5
    {0x4C,0x85,0x3E,0x2D}, //612
    {0x4C,0x95,0x3E,0x2D}, //612.5
    {0x4C,0xA5,0x3E,0x2D}, //613
    {0x4C,0xB5,0x3E,0x2D}, //613.5
    {0x4C,0xC5,0x3E,0x2D}, //614
    {0x4C,0xD5,0x3E,0x2D}, //614.5
    {0x4C,0xE5,0x3E,0x2D}, //615
    {0x4C,0xF5,0x3E,0x2D}, //615.5
    {0x4D,0x05,0x3E,0x2D}, //616
    {0x4A,0xB5,0x3E,0x2D}, //597.5
    {0x4E,0x35,0x3E,0x2D}, //625.5
    {0x4E,0x45,0x3E,0x2D}, //626
    {0x4E,0x55,0x3E,0x2D}, //626.5
    {0x4E,0x65,0x3E,0x2D}, //627
    {0x4E,0x75,0x3E,0x2D}, //627.5
    {0x4E,0x85,0x3E,0x2D}, //628
    {0x4E,0xA5,0x3E,0x2D}, //629
    {0x4E,0xC5,0x3E,0x2D}, //630
    {0x4E,0xE5,0x3E,0x2D}, //631
    {0x4E,0xF5,0x3E,0x2D}, //631.5    
};

/**
 * @brief 使用指数退避算法计算重试延迟
 * @param retry_attempt 当前重试次数（从0开始）
 * @return 延迟时间（毫秒）
 */
static uint32_t ldsBk9532CalculateRetryDelay(uint8_t retry_attempt)
{
    uint32_t delay = g_bk9532I2cConfig.retry_delay_base_ms * (1 << retry_attempt);
    if (delay > g_bk9532I2cConfig.retry_delay_max_ms) {
        delay = g_bk9532I2cConfig.retry_delay_max_ms;
    }
    return delay;
}

/**
 * @brief 更新I2C错误统计
 * @param success 操作是否成功
 */
static void ldsBk9532UpdateI2cStats(bool success)
{
    if (success) {
        g_bk9532I2c_consecutive_failures = 0;
    } else {
        g_bk9532I2c_consecutive_failures++;
    }
}

/**
 * @brief 检查是否需要芯片重新初始化
 * @return 如果应该触发重新初始化则返回true
 */
static bool ldsBk9532ShouldReinitialize(void)
{
    return (g_bk9532I2c_consecutive_failures >= g_bk9532I2cConfig.reinit_threshold);
}

/**
 * @brief 执行全面的芯片重新初始化
 * @return 成功返回0，错误返回负值
 */
static int ldsBk9532PerformReinitialize(void)
{
    LOG_W("BK9532 re-initialization triggered due to consecutive I2C failures");

    // 重置重新初始化的统计信息
    g_bk9532I2c_consecutive_failures = 0;

    // 硬件复位序列
    if (ce_pin >= 0) {
        rt_pin_write(ce_pin, PIN_LOW);
        rt_thread_mdelay(10);
        rt_pin_write(ce_pin, PIN_HIGH);
        rt_thread_mdelay(100);
    }

    // 重新配置默认寄存器
    lds_bk9532_set_regvalue_default();
    rt_thread_mdelay(10);

    // 如果可用，设置频率
    if (g_u8ChannelSeq > 0) {
        lds_bk9532_set_freq(g_u8ChannelSeq);
    }

    // 验证芯片ID以确认重新初始化成功
    uint8_t u8ReadVal[4] = {0};
    if (lds_bk9532_ReadData(u8ReadVal, 4, 0x70) == 0) {
        if ((u8ReadVal[2] == 0x95) && (u8ReadVal[3] == 0x32)) {
            LOG_I("BK9532 re-initialization successful, ChipId: %02X%02X",
                  u8ReadVal[2], u8ReadVal[3]);
            return 0;
        } else {
            LOG_E("BK9532 re-initialization failed, invalid ChipId: %02X%02X",
                  u8ReadVal[2], u8ReadVal[3]);
            return -1;
        }
    } else {
        LOG_E("BK9532 re-initialization failed, cannot read ChipId");
        return -1;
    }
}

//初始化
void lds_bk9532_i2c_gpio_init(void)
{

}


//读BK9532寄存器
uint8_t lds_bk9532_ReadData(uint8_t* dat,uint8_t cnt,uint8_t regAddr)
{
    rt_ssize_t ret = 0;
    uint8_t retry_count = 0;
    bool operation_success = false;

    if (dat == RT_NULL || cnt == 0) {
        LOG_E("Invalid parameters for read operation");
        return -1;
    }

    for (retry_count = 0; retry_count <= g_bk9532I2cConfig.retry_count; retry_count++) {
        uint8_t addr = regAddr << 1 | 1; // 读数据时，地址需要左移一位并加1

        // 发送寄存器地址
        ret = rt_i2c_master_send(i2c_dev, BK9532_I2C_ADDR,
                                RT_I2C_NO_STOP | RT_I2C_DEV_ID | RT_I2C_IGNORE_NACK,
                                &addr, 1);
        if (ret < 0) {
            if (retry_count < g_bk9532I2cConfig.retry_count) {
                LOG_W("I2C send addr failed! ret = %d, retry %d/%d",
                      ret, retry_count + 1, g_bk9532I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9532CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C send addr failed after %d retries! ret = %d",
                      g_bk9532I2cConfig.retry_count, ret);
                break;
            }
        }

        // 接收数据
        ret = rt_i2c_master_recv(i2c_dev, regAddr, RT_I2C_NO_START, dat, cnt);
        if (ret < 0) {
            if (retry_count < g_bk9532I2cConfig.retry_count) {
                LOG_W("I2C recv reg 0x%02x failed! ret = %d, retry %d/%d",
                      regAddr, ret, retry_count + 1, g_bk9532I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9532CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C recv reg 0x%02x failed after %d retries! ret = %d",
                      regAddr, g_bk9532I2cConfig.retry_count, ret);
                break;
            }
        }

        // 操作成功
        operation_success = true;
        break;
    }

    // 更新统计信息
    ldsBk9532UpdateI2cStats(operation_success);

    // 检查是否因连续失败需要重新初始化
    if (!operation_success && ldsBk9532ShouldReinitialize()) {
        LOG_W("Triggering BK9532 re-initialization due to I2C read failures");
        ldsBk9532PerformReinitialize();
    }

    return operation_success ? 0 : -1;
}

//写BK9532寄存器
uint8_t lds_bk9532_WriteData(const uint8_t* dat,uint8_t cnt,uint8_t regAddr)
{
    rt_ssize_t ret = 0;
    uint8_t retry_count = 0;
    bool operation_success = false;

    if (dat == RT_NULL || cnt == 0) {
        LOG_E("Invalid parameters for write operation");
        return -1;
    }

    for (retry_count = 0; retry_count <= g_bk9532I2cConfig.retry_count; retry_count++) {
        uint8_t addr = regAddr << 1;

        // Send register address
        ret = rt_i2c_master_send(i2c_dev, BK9532_I2C_ADDR,
                                RT_I2C_NO_STOP | RT_I2C_DEV_ID | RT_I2C_IGNORE_NACK,
                                &addr, 1);
        if (ret < 0) {
            if (retry_count < g_bk9532I2cConfig.retry_count) {
                LOG_W("I2C send addr failed! ret = %d, retry %d/%d",
                      ret, retry_count + 1, g_bk9532I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9532CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C send addr failed after %d retries! ret = %d",
                      g_bk9532I2cConfig.retry_count, ret);
                break;
            }
        }

        // 发送数据
        ret = rt_i2c_master_send(i2c_dev, regAddr, RT_I2C_NO_START, dat, cnt);
        if (ret < 0) {
            if (retry_count < g_bk9532I2cConfig.retry_count) {
                LOG_W("I2C send reg 0x%02x data failed! ret = %d, retry %d/%d",
                      regAddr, ret, retry_count + 1, g_bk9532I2cConfig.retry_count);
                rt_thread_mdelay(ldsBk9532CalculateRetryDelay(retry_count));
                continue;
            } else {
                LOG_E("I2C send reg 0x%02x data failed after %d retries! ret = %d",
                      regAddr, g_bk9532I2cConfig.retry_count, ret);
                break;
            }
        }

        // 操作成功
        operation_success = true;
        break;
    }

    // 更新统计信息
    ldsBk9532UpdateI2cStats(operation_success);

    // 检查是否因连续失败需要重新初始化
    if (!operation_success && ldsBk9532ShouldReinitialize()) {
        LOG_W("Triggering BK9532 re-initialization due to I2C write failures");
        ldsBk9532PerformReinitialize();
    }

    return operation_success ? 0 : -1;
}

//写BK9532芯片的默认寄存器
void lds_bk9532_set_regvalue_default(void)
{
    uint8_t u8addr = 0;
    uint8_t i = 0;

    for(i=0;i<BK9532_REG_VAL_NUM;i++)
    {
        if(i <= 13)         //REG0x0~0x0D--0-13
            u8addr = i;
        else if( i <= 17)   //REG2C~REG2F
            u8addr = (0x2C - 14) + i;
        else if( i <= 33    )//0x30~0x3F
            u8addr = (0x30 - 18) + i;
        else if( i <= 39)   //REG59~REG5E
            u8addr = (0x59 - 34) + i;
        else if( i <= 53)   //REG0x70~0x7F,只读
            u8addr = (0x70 - 40) + i;  
        
        lds_bk9532_WriteData(bk9532_reg_val[i],REG_VAL_CNT,u8addr); //rx_reg_val=rx_48k_uband values
    }
}


//软件复位BK9532-0 REG3F, 0x8D, 0x7A, 0x00, 0x2F
void lds_bk9532_reset_chip_bk9532(void)
{
    uint8_t u8reg_val[4]={0x00};

    lds_bk9532_ReadData(u8reg_val,4,0x3F); 
    u8reg_val[3] &= ~0x20;//
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
    u8reg_val[3] |= 0x20;//
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
}


//写BK9532芯片的REG3F的值-12,RX接收使能
void lds_bk9532_set_reg3F_value_31bit(void)
{
    uint8_t u8reg_val[4]={0x8D, 0x79, 0x00, 0x2F};//0x79 ANT2-->0x78 ANT1-->0x79 ANT2,

    u8reg_val[0] &= ~0x80;//
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
    u8reg_val[0] |= 0x80;//
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
}


//写BK9532芯片的REG3F的值-15,RX-PLC使能
void lds_bk9532_set_reg3F_value_28bit(void)
{
    uint8_t u8reg_val[4]={0x8D, 0x79, 0x00, 0x2F};//0x79 ANT2-->0x78 ANT1-->0x79 ANT2,

    u8reg_val[0] |= 0x10;//
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
    u8reg_val[0] &= ~0x10;//
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
}

//写BK9532芯片的REG3F的值-16,RX-AFC使能
void lds_bk9532_set_reg3F_value_Set26bit(void)
{
    uint8_t u8reg_val[4]={0x8D, 0x79, 0x00, 0x2F};//0x79 ANT2-->0x78 ANT1-->0x79 ANT2,

    lds_bk9532_ReadData(u8reg_val,4,0x3F); 
    u8reg_val[0] |= 0x04;//bit26-AFC使能
    lds_bk9532_WriteData(u8reg_val,4,0x3F); 
    
}


//写BK9532芯片的REG03对应的是U段频点配置-1/6/7
void lds_bk9532_set_reg03_value(uint8_t *pu8regvalue)
{
    lds_bk9532_WriteData(pu8regvalue,4,0x03);
}


//写BK9532芯片的REG0D的值--U段里面的频点-2
int lds_bk9532_set_reg0D_lookfor_table(uint8_t u8TableIndex)
{
    uint8_t u8RegBuf[4] = {0};
    uint8_t u8RegBufRead[4] = {0};
    
    u8RegBuf[0] = bk9532_reg_freq[u8TableIndex][0];
    u8RegBuf[1] = bk9532_reg_freq[u8TableIndex][1];
    u8RegBuf[2] = bk9532_reg_freq[u8TableIndex][2];
    u8RegBuf[3] = bk9532_reg_freq[u8TableIndex][3];
    lds_bk9532_WriteData(u8RegBuf,4,0x0D);
    
    LOG_I("Set Freq %02X,%02X,%02X,%02X,%02X\n",u8TableIndex,bk9532_reg_freq[u8TableIndex][0],bk9532_reg_freq[u8TableIndex][1]
                                                ,bk9532_reg_freq[u8TableIndex][2],bk9532_reg_freq[u8TableIndex][3]);
    //读取0x0D寄存器的值，并与设置值比较
    lds_bk9532_ReadData(u8RegBufRead,4,0x0D);
    if(rt_memcmp(u8RegBufRead, u8RegBuf,4) == 0)
    {
        LOG_I("Set Freq Success\n");
        return 0;
    }
    else
    {
        LOG_E("Set Freq Fail set %08X, get %08X\n", *((uint32_t *)u8RegBuf), *((uint32_t *)u8RegBufRead));
        return -1;
    }
}


//写BK9532芯片的REG39的值--用户ID-3
void lds_bk9532_set_reg39_value(void)
{
    if(!DevIsRegMode())//正常工作状态
        lds_bk9532_WriteData(USER_ID_BUF,4,0x39);
    else
        lds_bk9532_WriteData(USER_ID_BUF_TEST,4,0x39);
    return;
}


//写BK9532芯片的REG06的值-4/11
void lds_bk9532_set_reg06_value(uint8_t *pu8regvalue)
{
    lds_bk9532_WriteData(pu8regvalue,4,0x06);
}


//写BK9532芯片的REG07的值-5/10
void lds_bk9532_set_reg07_value(uint8_t *pu8regvalue)
{
    lds_bk9532_WriteData(pu8regvalue,4,0x07);
}


//写BK9532芯片的REG04的值-8/9
void lds_bk9532_set_reg04_value(uint8_t *pu8regvalue)
{
    lds_bk9532_WriteData(pu8regvalue,4,0x04);
}


//写BK9532芯片的REG3D的值-13
void lds_bk9532_set_reg3D_value_bit14(void)
{
    uint8_t u8reg_val[4]={0x00, 0x00, 0x26, 0x29};

    lds_bk9532_WriteData(u8reg_val,4,0x3D); 
}


//写BK9532芯片的REG3B的值-14
void lds_bk9532_set_reg3B_value(void)
{
    uint8_t u8reg_val[4]={0x6D, 0x00, 0x08, 0x00};

    lds_bk9532_WriteData(u8reg_val,4,0x3B); 
}

//更改设备工作信道频点
void lds_bk9532_set_freq(uint8_t u8SeqCh)
{
    uint8_t u8Reg03_buf0[4]={0x24, 0x52, 0x26, 0x9F};
    uint8_t u8Reg06_buf0[4]={0x1B, 0xED, 0xFB, 0x00};
    uint8_t u8Reg07_buf0[4]={0x1E, 0x2E, 0xC5, 0xAA};

    uint8_t u8Reg03_buf1[4]={0x24, 0x12, 0x26, 0x9F};
    uint8_t u8Reg03_buf2[4]={0x24, 0x52, 0x26, 0x9F};
    uint8_t u8Reg04_buf0[4]={0x50, 0x88, 0x00, 0x44};
    uint8_t u8Reg04_buf1[4]={0x52, 0x88, 0x00, 0x44};
    uint8_t u8Reg07_buf1[4]={0x1C, 0x2E, 0xC5, 0xAA};
    uint8_t u8Reg06_buf1[4]={0x5B, 0xED, 0xFB, 0x00};
     
    rt_pin_write(ce_pin, PIN_HIGH);
    lds_bk9532_reset_chip_bk9532();//软件复位BK9532,开始写入配置
    rt_thread_mdelay(10);
    lds_bk9532_set_reg03_value(u8Reg03_buf0);
    g_u8SetFreqCnt = lds_bk9532_set_reg0D_lookfor_table(u8SeqCh);
    lds_bk9532_set_reg39_value();
    lds_bk9532_set_reg06_value(u8Reg06_buf0);
    lds_bk9532_set_reg07_value(u8Reg07_buf0);
    rt_thread_mdelay(1);
    lds_bk9532_set_reg03_value(u8Reg03_buf1);
    lds_bk9532_set_reg03_value(u8Reg03_buf2);
    lds_bk9532_set_reg04_value(u8Reg04_buf0);
    lds_bk9532_set_reg04_value(u8Reg04_buf1);
    lds_bk9532_set_reg07_value(u8Reg07_buf1);
    lds_bk9532_set_reg06_value(u8Reg06_buf1);
    lds_bk9532_set_reg3F_value_31bit();
    rt_thread_mdelay(10);
    lds_bk9532_set_reg3D_value_bit14();
    lds_bk9532_set_reg3B_value();
    lds_bk9532_set_reg3F_value_28bit();
    lds_bk9532_set_reg3F_value_Set26bit();//AFC-使能

    return;
}

static uint16_t s_u16CheckI2cTimer = 0;
static uint8_t s_u8DisconnectBk9532Cnt = 0;

void lds_bk9532_get_status(void)
{
    uint32_t u32Value = 0;
    uint32_t u32ValueBit27 = 0x08000000;
    uint8_t u8ReadVal[4]={0x00,0x00,0x00,0x00};

    // 60秒检查一次I2C的连接状态
    if((!DevIsRegMode())&&(s_u16CheckI2cTimer++ >= 60))
    {
        lds_bk9532_ReadData(u8ReadVal,4,0x70);//读ID判断I2C是否OK
        LOG_I("Reg0x70 ChipId,%02X,%02X,%04X,%02X\n",u8ReadVal[2],u8ReadVal[3],s_u16CheckI2cTimer,s_u8DisconnectBk9532Cnt);
        if((u8ReadVal[2] != 0x95)||(u8ReadVal[3] != 0x32))
        {
            sBk9532RegStatus.u8Lock = 0x80;//置为失锁
            s_u8DisconnectBk9532Cnt++;

            // 更新I2C错误统计以监控失败
            g_bk9532I2c_consecutive_failures++;

            LOG_W("BK9532 ChipId check failed: %02X%02X, count: %d",
                  u8ReadVal[2], u8ReadVal[3], s_u8DisconnectBk9532Cnt);

            if(s_u8DisconnectBk9532Cnt >= 5)//重启
            {
                s_u8DisconnectBk9532Cnt = 0;
                LOG_E("BK9532 persistent failure, system restart may be needed");
                // 待办：重启系统？
            }
            else//重新初始化BK9532
            {
                // 使用增强的重新初始化机制
                if (ldsBk9532PerformReinitialize() == 0) {
                    LOG_I("BK9532 re-initialization successful");
                } else {
                    LOG_E("BK9532 re-initialization failed");
                }
            }
        }
        else
        {
            s_u8DisconnectBk9532Cnt = 0;
            // 成功读取芯片ID时重置连续失败计数
            g_bk9532I2c_consecutive_failures = 0;
        }
        s_u16CheckI2cTimer = 0;

        return;
    }

    rt_memcpy((uint8_t *)&sBk9532RegStatusLast,(uint8_t *)&sBk9532RegStatus,sizeof(sBk9532RegStatus));
    if(lds_bk9532_ReadData(u8ReadVal,4,0x7C))
        u32Value = u8ReadVal[0]*0x1000000 + u8ReadVal[1]*0x10000 + u8ReadVal[2]*0x100 + u8ReadVal[3];
    else
        return;

    sBk9532RegStatus.u8Lock = (uint8_t)((u32Value&u32ValueBit27)>>24);
    sBk9532RegStatus.u8UserData = (uint8_t)(u32Value&0x000000FF);
    LOG_I("Reg0x7C,%02X,UserData %02X\n",(u32Value&u32ValueBit27) ,(u32Value&0x000000FF));

    lds_bk9532_ReadData(u8ReadVal,4,0x75);//AF-RSSI强度指示
    sBk9532RegStatus.u8RfRssi = u8ReadVal[3];
    LOG_I("Reg0x75 RF-Rsi,%02X,%02X\n",u8ReadVal[3],(u8ReadVal[2]&0x0F));

    if(lds_bk9532_ReadData(u8ReadVal,4,0x79))//音频能量指示
        u32Value = u8ReadVal[0]*0x1000000 + u8ReadVal[1]*0x10000 + u8ReadVal[2]*0x100 + u8ReadVal[3];
    else
        return;
    sBk9532RegStatus.u16FltRssi = (uint16_t)(u32Value>>16);
    sBk9532RegStatus.u16AudRssi = (uint16_t)(u32Value&0x0000FFFF);
    LOG_I("Reg0x79 AF-Rsi,%04X,%04X\r\n",(uint16_t)(u32Value>>16),(uint16_t)(u32Value&0x0000FFFF));

    //lds_bk9532_ReadData(u8ReadVal,4,0x7B);
    lds_bk9532_ReadData(sBk9532RegStatus.u8Freq,4,0x0D);
    LOG_I("Reg0x0D Freq,%02X,%02X,%02X,%02X,%02X\n",g_u8ChannelSeq,
    sBk9532RegStatus.u8Freq[0],sBk9532RegStatus.u8Freq[1],sBk9532RegStatus.u8Freq[2],sBk9532RegStatus.u8Freq[3]);

    return;
}


static int lds_bk9532_hardware_init(void)
{
    i2c_dev = (struct rt_i2c_bus_device*)rt_device_find(BK9532_I2C_DEV_NAME);
    if (i2c_dev == RT_NULL) {
        LOG_E("I2C device not found!");
        return -1;
    }

    uint32_t data = 0;
    rt_err_t ret = lds_bk9532_ReadData((uint8_t *)&data, sizeof(data), 0x70);
    if (ret < 0) {
        LOG_E("I2C recv failed! ret = %d", ret);
        return -1;
    }
    LOG_I("I2C recv success! CHIP ID = 0x%08x", data);
    ce_pin = rt_pin_get(BK9532_CE_PIN);
    if (ce_pin < 0) {
        LOG_E("CE pin %s not found!", BK9532_CE_PIN);
        return -1;
    }
    rt_pin_mode(ce_pin, PIN_MODE_OUTPUT);
    rt_pin_write(ce_pin, PIN_HIGH); // Set CE pin high
    return 0;
}

int lds_bk9532_init(uint8_t freq_index)
{
    if (lds_bk9532_hardware_init()) {
        return -1;
    }
    lds_bk9532_set_regvalue_default(); // 写I2C默认寄存器配置
    rt_thread_mdelay(10);
    lds_bk9532_set_freq(freq_index);
    return 0;
}
/*
void bk9532_test(int argc, char *argv[])
{
    uint8_t freq_index = 0;
    if (argc == 2) {
        freq_index = atoi(argv[1]);
    } else {
        freq_index = 0;
    }
    LOG_I("BK9532 test, freq_index = %d", freq_index);
    lds_bk9532_init(freq_index);
    
}
MSH_CMD_EXPORT(bk9532_test, bk9532_test);
*/